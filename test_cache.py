#!/usr/bin/env python3
"""
Test skript pro ověření funkcionality cache systému
"""

import json
import os
from datetime import datetime

def test_cache():
    """Testuje, zda cache soubor existuje a obsahuje validní data"""
    cache_file = 'data_cache.json'
    
    if not os.path.exists(cache_file):
        print("❌ Cache soubor neexistuje")
        return False
    
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        required_keys = ['columns', 'data', 'last_update', 'cache_timestamp']
        missing_keys = [key for key in required_keys if key not in cache_data]
        
        if missing_keys:
            print(f"❌ Cache soubor neobsahuje požadované klíče: {missing_keys}")
            return False
        
        print("✅ Cache soubor je validní")
        print(f"📊 Počet sloupců: {len(cache_data['columns'])}")
        print(f"📈 Počet záznamů: {len(cache_data['data'])}")
        print(f"🕒 Poslední aktualizace: {cache_data['last_update']}")
        print(f"⏰ Cache timestamp: {cache_data['cache_timestamp']}")
        
        return True
        
    except json.JSONDecodeError:
        print("❌ Cache soubor obsahuje nevalidní JSON")
        return False
    except Exception as e:
        print(f"❌ Chyba při čtení cache: {str(e)}")
        return False

if __name__ == '__main__':
    print("🧪 Testování cache systému...")
    print("=" * 50)
    test_cache()
