# -*- coding: utf-8 -*-
"""
Created on Tue Aug 20 16:34:01 2019

@author: <PERSON><PERSON><PERSON><PERSON>, VaH<PERSON>zlik, MicZbor<PERSON>
"""

import os
import json
from shutil import copyfile 
from datetime import datetime,timedelta
# import time
import numpy as np
import pandas as pd

try:
    import keyring
except ImportError:
    keyring = None
    
try:
    import redis
except ImportError:
    redis = None
   
import platform

# import yaml
import base64
# import win32
# from win32com.client import Dispatch
# from exchangelib import DELEGATE, IMPERSONATION, Account, Credentials,Configuration,Message, Mailbox
# import requests
import time
import oracledb
import importlib
import subprocess
import sys



def makeDictFactory(curs):
    columnNames = [d[0] for d in curs.description]
    def createRow(*args):
        return dict(zip(columnNames, args))
    return createRow

# proměnná do env pro oracle, aby podporoval diakritiku
os.environ['NLS_LANG'] = '.AL32UTF8' 

def send_teams_notification(title = 'Fallback k emailové notifikaci', robot = None, detail = None):
    import os
    import requests
    resources = r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources'
    sys.path.append(resources)
    
    from read_DB_data import Rpa_DB
    MojeRpa_DB = Rpa_DB(env = 'PROD')
    if not robot:
        robot = os.environ.get('USERNAME')

    if not detail:
        detail = 'Tady bude detail.'
        
    if not title:
        title = 'Fallback k emailové notifikaci'

    user,webhook_url = MojeRpa_DB.get_cred_by_TIA_login('rpa_notify_hook')
    message = {
  "attachments": [
    {
      "contentType": "application/vnd.microsoft.card.adaptive",
      "content": {
        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
        "type": "AdaptiveCard",
        "version": "1.5",
        "body": [
          {
            "type": "TextBlock",
            "text": title,
            "weight": "Bolder",
            "size": "Medium"
          },
          {
            "type": "TextBlock",
            "text": robot,
            "wrap": True
          },
          {
            "type": "TextBlock",
            "text": detail,
            "wrap": True
          }
        ]
      }
    }
  ]
}

    
    proxyDict = {
 "http":"http://proxyns.cpas.cz:8080",
 "https": "http://**********:8080",
                }
    
    response = requests.post(
        url=webhook_url,
        headers={"Content-Type": "application/json"},
        json= message,
        proxies=proxyDict,
        verify=False)
    
    print(response.content)
    vars(response)

def send_mail_ex(text,recipient = '<EMAIL>', subject = 'RPA_notifikace_exchange'):
    import smtplib
    import os
    import sys
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    resources = r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources'
    sys.path.append(resources)
    
    from read_DB_data import Rpa_DB
    MojeRpa_DB = Rpa_DB(env = 'PROD')
    curs = MojeRpa_DB.conn.cursor()
    sql_query = "select ENTRY from robots_list where robot_user = 'Techrobot1'"
    curs.execute(sql_query)
    rows = curs.fetchall()
    password = rows[0][0]

    email_sender = '<EMAIL>'
    # Vytvoření zprávy
    msg = MIMEMultipart()
    msg["From"] = email_sender
    msg["To"] = recipient
    msg["Subject"] = subject

    body = text
    msg.attach(MIMEText(body, "plain"))

    try:
        server = smtplib.SMTP('smtp.cpas.cz', 25)
        server.starttls()
        server.login('<EMAIL>', password)  # pokud bude potřeba
        server.sendmail('<EMAIL>', recipient, msg.as_string())
        server.quit()
        print("Email odeslán.")
    except smtplib.SMTPException as e:
        print("Error sending email:", str(e))
        send_teams_notification(title = 'Fallback k emailové notifikaci', robot = os.environ['USERNAME'], detail = text)

        

class Rpa_DB():
    def __init__(self, config_yaml = None, env = 'DEV'):
        self.env = env
        print('Env_read_db_data: '+env)
        # oracle_path = os.path.join('C:'+os.sep,'Oracle','instantclient')
        # ##### insert the path to the Oracle dll to the PATH variables#######
        # path = os.environ['PATH']
        # path_list = path.split(';')
        # oraclecount = 0
        # for index, item in enumerate(path_list):
        #     if 'oracle\\instantclient' in item.lower():
        #         path_list[index] = oracle_path
        #         oraclecount = oraclecount + 1
        # if oraclecount == 0:
        #     path_list.append(oracle_path)
        # newpath = ';'.join(path_list)   
        # os.environ['PATH']=newpath 
        # =============================================================================
        
        if self.env == 'PROD':
            system = platform.system()
            if os.environ.get('COMPUTERNAME') == 'CZSSASAS1T-N':
                import json
                with open(r"C:\Users\<USER>\Desktop\flask_dash\config.txt", "r") as file:
                    conf_data = file.read()
                password = json.loads(conf_data).get('pasword_rpa')
            else:
                if system == 'Linux':
                    # Redis klíč může být např. 'RPA_DB_PROD:RPA'
                    password = self.get_password_from_redis('RPA_DB_PROD:RPA')
                else:
                    password = keyring.get_password('RPA_DB_PROD', 'RPA')
            
               
            connection_string = 'rpa/'+password+'@sunprod01.cpoj.cz:1521/rpa' # vyvoje oracle DB pro RPA
        else: 
            print('Connecting to test DB')
            password = keyring.get_password("RPA_DB_DEV", "RPA") # keyring to retrieve password
            connection_string = 'rpa/'+password+'@Suntest06.cpoj.cz:1530/rpadev' # vyvoje oracle DB pro RPA

        if not password:
            raise Exception('Není nastaven keyring: ' + str(__class__))
            
        self.connection_string = connection_string
        self.connection_str = connection_string
        self.conn = None
        
    @property
    def conn(self):
        if not self._conn:
            self.conn = oracledb.connect(self.connection_string)
        return self._conn

    @conn.setter
    def conn(self, value):
        self._conn = value

    def get_password_from_redis(self,redis_key, redis_host='localhost', redis_port=6379, redis_db=0):
        """
        Získá heslo z Redis pro daný klíč.
     
        Funkce je kompatibilní se dvěma způsoby uložení:
        1) prostý string (GET redis_key)
        2) hash s polem 'password' (HMGET redis_key password), které je uloženo v base64
     
        Args:
            redis_key (str): Název klíče v Redis. Pro hashový způsob používáme vzor
                '<process_name>:<tia_login>' (např. 'F9:DK_REP_rdr' nebo 'RPA_DB_PROD:RPA').
            redis_host (str): Host Redis serveru.
            redis_port (int): Port Redis serveru.
            redis_db (int): Index databáze v Redis.
     
        Returns:
            str: Dekódované heslo (plain text).
     
        Raises:
            ImportError: Pokud není dostupný modul redis.
            Exception: Pokud heslo pro zadaný klíč nebylo nalezeno.
        """
        if redis is None:
            raise ImportError('redis modul není nainstalován')
        r = redis.StrictRedis(host=redis_host, port=redis_port, db=redis_db, decode_responses=True)
     
        key_type = r.type(redis_key)
        # 1) pokud je klíč typu string, použij prosté GET
        if key_type == 'string':
            value = r.get(redis_key)
            if value:
                return value
     
        # 2) pokud je klíč typu hash, vezmi pole 'password' (base64)
        if key_type == 'hash':
            b64pwd = r.hget(redis_key, 'password')
            if b64pwd:
                try:
                    return base64.b64decode(b64pwd).decode('utf-8')
                except Exception:
                    # pokud není validní base64, vrať surovou hodnotu
                    return b64pwd
     
        raise Exception(f'Heslo pro {redis_key} nebylo nalezeno v redisu')
        
    def celni_skla_loader(self):
        """ Načte csv vygenerované Romanem a nainsertuje ho do RPA.Robot_queue"""
        print("Čelní skla started")
            
        celni_skla_file_name = 'Robot_skla_TTE188.csv'
       
        if self.env == 'PROD':
            celni_skla_archive_path = os.path.join(os.sep+os.sep,'sfsl02','dokumenty','RPA_LPU','_DATA','PROD','Celni_Skla','archive')
            celni_skla_input_path = os.path.join(os.sep+os.sep,'sfsl02','dokumenty','RPA_LPU','_DATA','PROD','Celni_Skla')
        else:
            celni_skla_archive_path = os.path.join(os.sep+os.sep,'sfsl02','dokumenty','RPA_LPU','_DATA','TEST','Celni_Skla','archive')
            celni_skla_input_path =  os.path.join(os.sep+os.sep,'sfsl02','dokumenty','RPA_LPU','_DATA','TEST','Celni_Skla')
            
        process_name = 'Celni_Skla'
        if os.path.exists(os.path.join(celni_skla_input_path,celni_skla_file_name)):
            print ("File exists")
            # self.check_new_for_skipping(process_name)
            df_base = self.Celni_Skla_loader_checker(process_name)
                # df získaný selectem z RQ podle processname, kde status je new, postponed nebo in progress
            df_csv = pd.DataFrame()
                # prázný DF pro naplnění 
            
            with open (os.path.join(celni_skla_input_path,celni_skla_file_name),"r") as input_file:
                csv_data = pd.read_csv(input_file, sep = ';', encoding = 'windows-1250', index_col=False)
                csv_data = csv_data.replace(np.nan, '', regex=True)
                csv_data = csv_data.sort_values(by='PU', ascending=False)
                csv_data = csv_data.astype({"PU": str})
                priority = 13
                for index, row in csv_data.iterrows(): 
                    spec_data = {}
                    spec_data['PU'] = str(row['PU'])
                    spec_data['KOD'] = str(row['KOD'])
                    spec_data['POZN'] = str(row['POZN'])
                    spec_data['NFELUSER'] = str(row['NFELUSER'])
        # data for single upload data dict
                    rq_data = {}
                    rq_data['STATUS'] = str("NEW")
                    rq_data['REFERENCE'] = str(row['PU'])
                    if  int(row['PU']) > 7000000000:
                        rq_data['ITEM_PRIORITY'] = priority +200
                    else:
                        rq_data['ITEM_PRIORITY'] = priority
                    rq_data['ROBOT'] = str(os.environ.get('USERNAME'))
                    rq_data['PROCESS_NAME'] = str(process_name)
                    rq_data['CREATION_DATE'] = datetime.now()
                    rq_data['PROCESS_BRANCH'] = str(row['KOD'])
                    rq_data['SPECIFIC_DATA'] = str(json.dumps(spec_data, ensure_ascii = False))
                    rq_data['LAST_UPDATE'] = datetime.now()
                    rq_data['EXCEPTION_TEXT'] = ''
                    rq_data['SPECIFIC_DATA2'] = ''
                    rq_data['STARTED'] = ''
                    rq_data['ENDED'] = ''
                    
                    
                    
                    df_csv = df_csv.append(rq_data, ignore_index=True)
                    priority += 1
    
            df_old = df_base.merge(df_csv.drop_duplicates(), on=['REFERENCE','SPECIFIC_DATA'], 
                   how='left', indicator=True)    
            df_old_records = df_old.loc[df_old['_merge'] == 'left_only']
                # data jsou v dtb a nejsou ve file, tyto budeme updatovat na hodnotu, přeskočeno - Status = Success BE = outdated
            df_old_final = pd.DataFrame() 
            df_old_final = df_old_records[['ID','SPECIFIC_DATA']]
            list_to_outdate = df_old_final.values.tolist()
                
                
            df_new = df_csv.merge(df_base.drop_duplicates(), on=['REFERENCE','SPECIFIC_DATA'], 
                    how='left', indicator=True)
            df_new_records = df_new.loc[df_new['_merge'] == 'left_only']
                # data jsou k reportování ve stavu NEW
            df_new_final = pd.DataFrame() 
            df_new_final = df_new_records[['STATUS_x','REFERENCE','ITEM_PRIORITY_x','PROCESS_NAME_x','CREATION_DATE_x','PROCESS_BRANCH_x','SPECIFIC_DATA','ROBOT_x','LAST_UPDATE_x','EXCEPTION_TEXT_x', 'SPECIFIC_DATA2_x', 'STARTED_x','ENDED_x']]
                
            list_to_insert = df_new_final.values.tolist()
        #            file archivation - common for single and mass upload
            rok = datetime.now().strftime("%Y")
            mesic = datetime.now().strftime("%m")
            den = datetime.now().strftime("%d")
            hodina = datetime.now().strftime("%H")
            minuta = datetime.now().strftime("%M")
            archive_name = celni_skla_file_name.split(".")[0]+"_"+rok+mesic+den+"_"+hodina+minuta+"."+celni_skla_file_name.split(".")[1]
            copyfile(os.path.join(celni_skla_input_path, celni_skla_file_name), os.path.join(celni_skla_archive_path, archive_name))
            os.remove(os.path.join(celni_skla_input_path, celni_skla_file_name))
        # mass upload 
        # for mass upload comment line 92 and uncomment  line 117
            
            self.RQ_mass_loader(list_to_insert)
            self.RQ_mass_oudater(list_to_outdate, process_name)
            
            print("New items inserted: "+str(len(list_to_insert)))
            print("Items outdated: "+str(len(list_to_outdate)))
            print("Čelní skla ended")
            
            # POSTPONED záznamy zpátky do NEW - odloženo z důvodu zpožděného uložení krycího dopisu do DA
            curs = self.conn.cursor()
            statement = """ UPDATE robot_queue SET status = 'NEW'
                            WHERE process_name = :process_name
                              and status = 'POSTPONED'
                              and bussines_except like '%Neexistující ID dokumentu v DA.%'
                            """
            curs.execute(statement,{'process_name' : process_name})
            self.conn.commit()
            curs.close()
        else:
            print ("File "+str(os.path.join(celni_skla_input_path,celni_skla_file_name))+ " does NOT exists")
            pass
        
    def Celni_Skla_loader_checker(self, process_name):
        sql_query = f"""select * from ROBOT_QUEUE where PROCESS_NAME = '{process_name}' and STATUS in ('NEW','POSTPONED','IN PROGRESS') """
        df_base  = pd.read_sql(sql_query,con = self.conn) 
        return (df_base)
    
    def search_item_by_ref (self, process_name, reference):
        """Vyhledá item v robot_queue podle process_name, reference"""
        curs = self.conn.cursor()
        sql_query = "SELECT * FROM robot_queue WHERE process_name = :process_name and reference = :reference order by last_update desc"
        curs.execute(sql_query, {'process_name': process_name,
                                 'reference': reference})
        rows = curs.fetchall()
        return rows
    
      
    def update_newAuction_total_car_auction(self,config_yaml, id_total_car_auction, data_for_log):
        
        def string_to_date(string):
            try:
                date_ob = datetime.datetime.strptime(string, '%Y-%m-%d %H:%M:%S')
                return date_ob
            except:
                pass
            try:
                date_ob = datetime.datetime.strptime(string, '%Y-%m-%d')
                return date_ob
            except:
                return(datetime.datetime.strptime("1911-01-01", '%Y-%m-%d'))
            
        
        firstRegistrationDate = data_for_log.get("firstRegistrationDate")
        technicka = data_for_log.get("technicka")
        
        
        if firstRegistrationDate != "":
            firstRegistrationDate = string_to_date(firstRegistrationDate)
        else:
            firstRegistrationDate = ""
            
        
        if technicka != "":
            technicka = string_to_date(technicka)
        else:
            technicka = ""
    
        
        connection_string = self.connection_string
        # insert new entry to DB
        with oracledb.connect(connection_string) as conn:
            curs = conn.cursor()
            
            # insert the ROBOT run
            sql_query_1 = """ UPDATE TOTAL_CAR_AUKCE
                            SET STATUS= :status,
                            WARNINGS_LIST=  :warnings_list,
                            ERRORS_LIST=  :errors_list,
                            CARTYPE=  :cartype,
                            MANUFACTURER=  :manufacturer,
                            MODEL=  :model,
                            FUEL=  :fuel,
                            MOTOR_CAPACITY=  :motor_capacity,
                            NEXT_TECH_CHECK = :next_check_tech_ob,
                            VIN=  :vin,
                            MILEAGE=  :mileage,
                            DAMAGE_DESC=  :damage_desc,
                            NOTE_INTERN=  :note_intern,
                            NOTE_BASIC=  :note_basic,
                            CAR_EQUIPMENT=  :car_equipment,
                            FIRST_REGISTRATION_DATE = :first_registration_date_ob,
                            LAST_UPDATE =:last_update
    
                            WHERE ID = :var2
                            
                        """
                        
            curs.execute(sql_query_1,
                         status =  "data_loaded",
                         warnings_list=  str(data_for_log.get("warning_list"))[:1000],
                         errors_list =  str(data_for_log.get("error_list"))[:1000],
                         cartype =  data_for_log.get("typVozidla")[:20],
                         manufacturer =  data_for_log.get("znackaVozidla")[:100],
                         model =  data_for_log.get("modelVozidla")[:200],
                         fuel =  data_for_log.get("zaklUdaje_palivo")[:20],
                         motor_capacity =  data_for_log.get("zaklUdaje_obsahMotoru")[:20],
                         next_check_tech_ob = technicka,
                         vin =  data_for_log.get("zaklUdaje_VIN")[:20],
                         mileage =  data_for_log.get("zaklUdaje_mileage")[:10],
                         damage_desc =  data_for_log.get("zaklUdaje_poskozeni")[:2000],
                         note_intern =  data_for_log.get("zaklUdaje_pozn_interni")[:2000],
                         note_basic =  data_for_log.get("zaklUdaje_poznamka")[:2000],
                         car_equipment =  data_for_log.get("zaklUdaje_vybava")[:2000],   
                         first_registration_date_ob = firstRegistrationDate,
                         last_update = datetime.datetime.now(),
            
                         var2 =  id_total_car_auction)
            conn.commit()
        
                
            return (data_for_log)
    
    
    
    
    def search_item_by_ref_dict (self, process_name, reference, process_branch = None):
        """Vyhledá item v robot_queue podle process_name, reference, případně process_branch"""
        curs = self.conn.cursor()
        if not process_branch:
            sql_query = "SELECT * FROM robot_queue WHERE process_name = :process_name and reference = :reference order by last_update desc"
            curs.execute(sql_query, {'process_name': process_name,
                                     'reference': reference})
        else:
            sql_query = "SELECT * FROM robot_queue WHERE process_name = :process_name and reference = :reference and process_branch = :process_branch order by last_update desc"
            curs.execute(sql_query, {'process_name': process_name,
                                     'reference': reference,
                                     'process_branch': process_branch})
            
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        return rows
    
    def RQ_mass_oudater(self,list_to_outdate, process_name):
             curs = self.conn.cursor() 
             curs.executemany("""
                  UPDATE ROBOT_QUEUE
                  SET STATUS = 'SUCCESS', BUSSINES_EXCEPT = 'outdated'
                  WHERE ID =:1 and SPECIFIC_DATA =:2
                              """, list_to_outdate)
             self.conn.commit()
             curs.close()
             print("Data updated for outdate.")

    def RQ_mass_loader(self,rq_data):
        ''' vstupem je list of dict - df_new_final.values.tolist()'''
        print(str(rq_data))
        curs = self.conn.cursor()     
        curs.executemany("""
               INSERT INTO ROBOT_QUEUE (STATUS,REFERENCE,ITEM_PRIORITY,PROCESS_NAME, CREATION_DATE, PROCESS_BRANCH, SPECIFIC_DATA, ROBOT, LAST_UPDATE, EXCEPTION_TEXT, SPECIFIC_DATA2, STARTED,ENDED)
           values (:status, :reference, :item_priority, :process_name, :creation_date, :process_branch, :specific_data, :robot, :last_update, :exception_text, :specific_data2,:started,:ended)""", rq_data)
        self.conn.commit()
        curs.close()
        print("Data_from_file_loaded_to_dtb.")
  
    
    def RQ_mass_loader_PRM(self,rq_data):
        ''' vstupem je list of dict - df_new_final.values.tolist()'''
        print(str(rq_data))
        curs = self.conn.cursor()     
        curs.executemany("""INSERT INTO RPA_PRM (COMPANYID,PARTNER,STREET,STREETNO,POSTALCODE,CITY,BRANDS,PHONE,EMAIL,TIAIDNO)
                                values (:companyid,:partner,:street,:streetno,:postalcode,:city, :brands,:phone,:email,:tiaidno)""", rq_data)
        self.conn.commit()
        curs.close()
        print("Data_from_file_loaded_to_dtb.")
    


        
    def RQ_single_loader(self, rq_data):
        """ insert do robot_queue vstup datadict obsahující tyto
            keys: STATUS, REFERENCE, ITEM_PRIORITY, PROCESS_NAME, CREATION_DATE, PROCESS_BRANCH, SPECIFIC_DATA, 
                  ROBOT, LAST_UPDATE, SPECIFIC_DATA2, STARTED, ENDED, RESULT, PROGRESS
            Vrací id nového zázanmu
        """
        curs = self.conn.cursor()
        idVar = curs.var(oracledb.DB_TYPE_NUMBER)
        sql_query_1 = """ INSERT INTO ROBOT_QUEUE (STATUS, REFERENCE, ITEM_PRIORITY, PROCESS_NAME, CREATION_DATE, PROCESS_BRANCH, SPECIFIC_DATA, ROBOT, LAST_UPDATE, SPECIFIC_DATA2, STARTED, ENDED, RESULT, PROGRESS)
                              VALUES (:status, :reference, :item_priority, :process_name, :creation_date, :process_branch, :specific_data, :robot, :last_update, :specific_data2, :started, :ended, :result, :progress) 
                              RETURNING id INTO :id
                    """
        curs.execute(sql_query_1,
                      {'status' : rq_data.get("STATUS"),
                      'reference' :  rq_data.get("REFERENCE"),
                      'item_priority' : rq_data.get("ITEM_PRIORITY"),
                      'process_name' : rq_data.get("PROCESS_NAME"),
                      'creation_date' : rq_data.get("CREATION_DATE"),
                      'process_branch' : rq_data.get("PROCESS_BRANCH"),
                      'specific_data' : rq_data.get("SPECIFIC_DATA"),
                      'specific_data2' : rq_data.get("SPECIFIC_DATA2"),
                      'started' : rq_data.get("STARTED"),
                      'ended' : rq_data.get("ENDED"),
                      'result' : rq_data.get("RESULT"),
                      'progress' : rq_data.get("PROGRESS"),
                      'robot' : rq_data.get("ROBOT"),
                      'last_update' : datetime.now(),
                      'id' : idVar
                      }
                  )
        self.conn.commit()
        id = idVar.getvalue()
        curs.close()
        print(str(rq_data) + " loaded")
        if id:
            return int(id[0])

    def REST_LOG_single_loader(self,rq_data):
        now = datetime.now()
        """ insert do rest_logs vstup datadict obsahující tyto
        keys: INPUT, OUTPUT, TIMESTAMP"""
        # příprava polí do dtb
    #    os.chdir(config_yaml['oracle'])
        # get connection string for DEV/PROD
        # insert new entry to DB
        curs = self.conn.cursor()
        # insert the ROBOT run
        sql_query_1 = """ INSERT INTO REST_LOGS (INPUT,OUTPUT,TIMESTAMP)
                                    VALUES (:input, :output, :timestamp)
                    """
        curs.execute(sql_query_1,
                     {'input' : rq_data.get("input"),
                     'output' :  rq_data.get("output"),
                     'timestamp' : now}
                     )
        self.conn.commit()
        curs.close()
        print(str(rq_data)+" loaded")
        
        
        
        
    def RQ_single_loader_NEW(self,rq_data):
        now = datetime.now()
        """ insert do robot_queue vstup datadict obsahující tyto
        keys: STATUS,REFERENCE,ITEM_PRIORITY,PROCESS_NAME, CREATION_DATE, PROCESS_BRANCH, SPECIFIC_DATA, ROBOT"""
        # příprava polí do dtb
        # get connection string for DEV/PROD
        # insert new entry to DB
        curs = self.conn.cursor()
        # insert the ROBOT run
        sql_query_1 = """ INSERT INTO ROBOT_QUEUE (STATUS,REFERENCE,ITEM_PRIORITY,PROCESS_NAME, CREATION_DATE, PROCESS_BRANCH, SPECIFIC_DATA, ROBOT, LAST_UPDATE,SPECIFIC_DATA2, EXCEPTION_TEXT)
                                    VALUES (:status, :reference, :item_priority, :process_name, :creation_date, :process_branch, :specific_data, :robot, :last_update,:specific_data2, :exception_text)
                    """
        curs.execute(sql_query_1,
                     {'status' : rq_data.get("STATUS"),
                     'reference' :  rq_data.get("REFERENCE"),
                     'item_priority' : rq_data.get("ITEM_PRIORITY",10),
                     'process_name' : rq_data.get("PROCESS_NAME"),
                     'creation_date' : rq_data.get("CREATION_DATE"),
                     'process_branch' : rq_data.get("PROCESS_BRANCH"),
                     'specific_data' : rq_data.get("SPECIFIC_DATA"),
                     'specific_data2' : rq_data.get("SPECIFIC_DATA2"),
                     'robot' : rq_data.get("ROBOT"),
                     'exception_text' : rq_data.get("EXCEPTION_TEXT"),
                     'last_update' : now
                     }
                     )
        self.conn.commit()
        curs.close()
        print(str(rq_data)+" loaded")

# updates value status in DB
# příklad volání
# update_robot_queue_status('robot_queue', '17581', item_priority = '25', status = 'IN_PROGRESS')
# update_robot_queue_status(config_yaml, 'robot_queue', '17581', muj_datadict_sloupcu)
    def update_robot_queue_status(self, table_name, itemId, **columns):
        # get connection string for DEV/PROD
        # print (connection_string)
        curs = self.conn.cursor()
        for column in columns.items():
            # ORACLE validace názvu cílové tabulky  - ochrana proti sql injection
            
            
            try:
                my_tabname = curs.callfunc('sys.dbms_assert.sql_object_name', oracledb.DB_TYPE_VARCHAR, [table_name])
            except oracledb.DatabaseError:
                print('Invalid tableName')

                #  todo nevím jestli nedodělat nějaký warning nebo BREAK??
                continue
            # ORACLE validace názvu cílového sloupce   - ochrana proti sql injection
                
            try:
                column_name = curs.callfunc('sys.dbms_assert.qualified_sql_name', oracledb.DB_TYPE_VARCHAR, [column[0]])
            except oracledb.DatabaseError:
                print('Invalid columnName')

                #  todo nevím jestli nedodělat nějaký warning nebo BREAK??
                continue
            statement = """ UPDATE {tableName} SET {columnName} = :col_var
                            , LAST_UPDATE = :now
                            WHERE ID = :var_id
                            """
                                                 
            curs.execute(statement.format(tableName=my_tabname, columnName = column_name),
                          var_id = itemId,
                          col_var = column[1],
                          now = datetime.now()
                          )
            self.conn.commit()
        curs.close()
        return 
    
    def update_robot_queue_attempt(self, itemId:int, attempt:int = ''):
        curs = self.conn.cursor()
        statement = """ UPDATE robot_queue SET attempt = :attempt
                        , LAST_UPDATE = :now
                        WHERE ID = :itemId
                        """
        
        #print(statement.format(tableName=my_tabname, columnName = column_name))                
                        
        curs.execute(statement,{'itemId' : itemId,
                                'attempt' : attempt,
                                'now': datetime.now()})
        self.conn.commit()
        curs.close()
        return     
    def update_robot_queue_progress(self, itemId:int, progress:str = ''):
        curs = self.conn.cursor()
        statement = """ UPDATE robot_queue SET progress = :progress
                        , LAST_UPDATE = :now
                        WHERE ID = :itemId
                        """
        
        #print(statement.format(tableName=my_tabname, columnName = column_name))                
                        
        curs.execute(statement,{'itemId' : itemId,
                                'progress' : progress,
                                'now': datetime.now()})
        self.conn.commit()
        curs.close()
        return     
    def get_robot_queue_progress(self, itemId:int):
        curs = self.conn.cursor()
        statement = """ select PROGRESS from robot_queue 
                        WHERE ID = :itemId
                        """
        curs.execute(statement,{'itemId' : itemId})
        rows = curs.fetchall()
        curs.close()
        progress = rows[0][0]
        return progress
    

    
    def rpa_log (self,
                 reference = None,
                 start_time = None,
                 end_time = None,
                 process = None,
                 task = None,
                 result = None,
                 detail = None,
                 t_error = None,
                 b_error = None,
                 robot = os.environ['USERNAME'],
                 process_branch = None,
                 rq_id = None):
        """ funkce pro zapisování robologů do databáze"""
        #print(json.dumps({'refer':reference,
        #                'inserted':datetime.now(),
        #                'start_time':start_time,
        #                'end_time':end_time,
        #                'process':process,
         #               'task':task,
        #                'result':result,
        #                'detail':detail,
         #               't_error':t_error,
         #               'b_error':b_error,
         #               'robot':robot,
          #              'process_branch':process_branch}))
        
        curs = self.conn.cursor()
      
        statement = """ INSERT INTO rpa_log (reference,inserted,start_time,end_time,process,task,result,detail,t_error,b_error,robot,process_branch,rq_id)
                                    VALUES (:refer,:inserted,:start_time,:end_time,:process,:task,:result, :detail,:t_error,:b_error,:robot,:process_branch,:rq_id)
                                """
        curs.execute(statement,{'refer':reference,
                        'inserted':datetime.now(),
                        'start_time':start_time,
                        'end_time':end_time,
                        'process':process,
                        'task':task,
                        'result':result,
                        'detail':detail,
                        't_error':t_error,
                        'b_error':b_error,
                        'robot':robot,
                        'process_branch':process_branch,
                        'rq_id': rq_id})
        self.conn.commit()
        curs.close()
        
    def get_interval(self,process_name, in_progres = False):
            curs = self.conn.cursor()
            sql_query = f"""
            select INTERVAL from PROCESS_SETUP where PROCESS_NAME ='{process_name}'
                            """
            curs.execute(sql_query)
            rows = curs.fetchall()
            curs.close()
            interval = rows[0][0]
            return interval

    def get_data(self,process_name):
            curs = self.conn.cursor()
            sql_query = f"""
            select DATA from PROCESS_SETUP where PROCESS_NAME ='{process_name}'
                            """
            curs.execute(sql_query)
            rows = curs.fetchall()
            curs.close()
            interval = rows[0][0]
            return interval
            
    def get_counter(self,process_name):
            curs = self.conn.cursor()
            sql_query = f"""
            select COUNTER from PROCESS_SETUP where PROCESS_NAME ='{process_name}' 
            """
            curs.execute(sql_query)
            rows = curs.fetchall()
            curs.close()
            counter = rows[0][0]
            return counter


    def get_postponed(self):
            curs = self.conn.cursor()
            sql_query = """A
            select ID, REFERENCE, EXCEPTION_TEXT, PROCESS_NAME, PROCESS_BRANCH, BUSSINES_EXCEPT from robot_queue where status ='POSTPONED' and trunc(LAST_UPDATE) = TRUNC(SYSDATE)
            """
            curs.execute(sql_query)
            rows = curs.fetchall()
            curs.close() 
            
            return rows
        
    def update_postponed(self, process_name = None, interval_min = 30):
        '''
        Funkce přepíná stavu do 'NEW' pro položky ve stavu 'POSTPONED', které byly aktualizovány déle než před x minutami
        ----------
        process_name : STR, mandatory
            jméno procesu. The default is None.
            interval_min : INT, mandatory
            interval v minutách použije se do select, kdy SYSdate je o x min větší než LAST_UPDATE
            '''


        
        curs = self.conn.cursor()
        statement =  """ UPDATE robot_queue
            SET status = 'NEW'
            WHERE process_name = :process_name
            AND status = 'POSTPONED'
            and last_update < sysdate - interval '1' MINUTE * :timedelta
                        """
        curs.execute(statement,{'process_name':process_name, 'timedelta':interval_min})
        self.conn.commit()
        curs.close()
        return   
        


    def get_new_item_dict(self, process_name, onlyNew = True, repeat_counter = 0, timedelta = '1', itemId = None):
        """Funkce na získání nového itemu z fronty, pokud jako arg zadáno ID itemu, tak stáhne tento item, současně hned změní status
        tohoto itemu na 'IN PROGRESS' pokud je parametr set_in_prog = True, jinak nedává do in progress
        Nastavením hodnoty STARTED do budoucne, lze odložit zpracování do této hodnoty.
        """
        # curs = conn.cursor()
        curs = self.conn.cursor()
        if itemId == None:
            if  onlyNew == True:
                sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT, SPECIFIC_DATA2, PROGRESS, RESULT
                                from ROBOT_QUEUE
                                where ID = (select min(id) keep (dense_rank first order by item_priority desc, trunc(creation_date))
                                            from robot_queue
                                            where process_name = :process_name
                                            and status = 'NEW'
                                            and nvl(started, sysdate) <= sysdate
                                            and not (item_priority = 0)) for update skip locked"""
                curs.execute(sql_query, {'process_name':process_name})
            # odbočka pro procesy, kde chceme nabírat i "IN PROGRESS" itemy, defaultně je vypnuto    
            else:
                sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT, SPECIFIC_DATA2, PROGRESS, RESULT
                                from ROBOT_QUEUE
                                where ID = (select min(id) keep (dense_rank first order by item_priority desc, trunc(creation_date))
                                            from robot_queue
                                            where process_name = :process_name
                                            and (status = 'NEW' or ((status = 'IN PROGRESS' OR status = 'POSTPONED') AND
                                                                   last_update < sysdate - interval '1' HOUR * :timedelta))
                                            and nvl(started, sysdate) <= sysdate
                                            and not (item_priority = 0)) for update skip locked"""              
                curs.execute(sql_query, {'process_name':process_name,'timedelta' : timedelta})
        else:
            sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT, SPECIFIC_DATA2, PROGRESS, RESULT
                     from ROBOT_QUEUE
                     where ID= :itemId for update skip locked"""
            curs.execute(sql_query, {'itemid':itemId})         
       
        column_names = list(map(lambda x: x.lower(), [d[0] for d in curs.description]))
        rows = curs.fetchall()
        if len(rows) > 0:
            result = [dict(zip(column_names, row)) for row in rows][0]
            
            attempt = result.get('attempt')
            if attempt is None:
                attempt = 1
            else:
                attempt = int(attempt) + 1
          
            sql_query = '''update ROBOT_QUEUE
                            set STATUS = 'IN PROGRESS',
                                STARTED = :now,
                                ROBOT = :robot,
                                ATTEMPT = :attempt,
                                LAST_UPDATE = :last_update
                            where ID = :id
                        '''
            curs.execute(sql_query, {
                                'id': result['id'],
                                'now': datetime.now(),
                                'robot': os.environ['USERNAME'],
                                'attempt': attempt,
                                'last_update':datetime.now()
                                }
                        )
            self.conn.commit()
            
        else:
            result = None 

        curs.close()
        return (result)    

    
    def get_new_item(self, process_name, onlyNew = True, repeat_counter = 0, timedelta = '1', itemId = None):
        """Funkce na získání nového itemu z fronty, pokud jako arg zadáno ID itemu, tak stáhne tento item, současně hned změní status
        tohoto itemu na 'IN PROGRESS' 
        Nastavením hodnoty STARTED do budoucne, lze odložit zpracování do této hodnoty.
        """
        curs = self.conn.cursor()
        if itemId == None:
            if  onlyNew == True:
                sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT,SPECIFIC_DATA2,PROGRESS
                                from ROBOT_QUEUE
                                where ID = (select min(id) keep (dense_rank first order by item_priority desc, trunc(creation_date))
                                            from robot_queue
                                            where process_name = :process_name
                                            and status ='NEW'
                                            and nvl(started, sysdate) <= sysdate
                                            and not (item_priority = 0)) for update skip locked"""
                curs.execute(sql_query, {'process_name':process_name})
            # odbočka pro procesy, kde chceme nabírat i "IN PROGRESS" itemy, defaultně je vypnuto    
            else:
                sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT,SPECIFIC_DATA2,PROGRESS
                                from ROBOT_QUEUE
                                where ID = (select min(id) keep (dense_rank first order by item_priority desc, trunc(creation_date))
                                            from robot_queue
                                            where process_name = :process_name
                                            and (status ='NEW' or ((status = 'IN PROGRESS' OR status = 'POSTPONED') AND
                                                                   last_update < sysdate - interval '1' HOUR * :timedelta))
                                            and nvl(started, sysdate) <= sysdate
                                            and not (item_priority = 0)) for update skip locked"""              
                curs.execute(sql_query, {'process_name': process_name, 'timedelta' : timedelta})
        else:
            sql_query = """select ID, SPECIFIC_DATA, PROCESS_NAME, PROCESS_BRANCH, STATUS, REFERENCE, ITEM_PRIORITY, CREATION_DATE, ATTEMPT,SPECIFIC_DATA2,PROGRESS
                     from ROBOT_QUEUE
                     where ID= :itemId for update skip locked"""
            curs.execute(sql_query, {'itemid':itemId})         
        # curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        if len(rows) > 0:
            data_list = rows[0]
            attempt = data_list[8]
            if attempt is None:
                attempt = 1
            else:
                attempt = int(attempt) + 1
            sql_query = '''update ROBOT_QUEUE
                            set STATUS = 'IN PROGRESS',
                                STARTED = :now,
                                ROBOT = :robot,
                                ATTEMPT = :attempt,
                                LAST_UPDATE = :last_update
                            where ID = :id
                        '''
            curs.execute(sql_query, {
                                'id':data_list[0],
                                'now': datetime.now(),
                                'robot': os.environ['USERNAME'],
                                'attempt': attempt,
                                'last_update':datetime.now()
                                }
                        )
            self.conn.commit()
            curs.close()
            
        else:
            data_list = None 
        return (data_list)    


    def get_new_waiting_items(self, process_name, process_branch): 
        """Vrátí list záznamů (dict) z robot_queue, které mají started null nebo je menší než sysdate"""
        curs = self.conn.cursor()
        sql_query = """ select * 
                        from robot_queue 
                        where process_name = :process_name
                          and process_branch = :process_branch
                          and status = 'WAITING'
                          and nvl(started, sysdate) <= sysdate
                    """
        curs.execute(sql_query, {'process_name': process_name,'process_branch': process_branch})
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows = curs.fetchall()
        return rows

    def get_new_waiting_items2(self, process_name, process_branch, id): 
        """Vrátí list záznamů (dict) z robot_queue, které mají started null nebo je menší než sysdate"""
        curs = self.conn.cursor()
        sql_query = """ select * 
                        from robot_queue 
                        where process_name = :process_name
                          and process_branch = :process_branch
                          and status = 'WAITING'
                          and id = :id
                    """
        curs.execute(sql_query, {'process_name': process_name,'process_branch': process_branch, 'id': id})
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows = curs.fetchall()
        return rows

    def get_waiting_items(self, process_name, process_branch): 
        """Vrátí list záznamů (dict) z robot_queue, které jsou v čekací lhůtě -> started > sysdate"""
        curs = self.conn.cursor()
        sql_query = """ select * 
                        from robot_queue 
                        where process_name = :process_name
                          and process_branch = :process_branch
                          and status = 'WAITING'
                          and started > sysdate
                    """
        curs.execute(sql_query, {'process_name': process_name,'process_branch': process_branch})
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows = curs.fetchall()
        return rows


    # Pro Celni_Skla_AfterReg - již se nepoužívá
    def celSkla_getLastAssignUser(self, dict_likv):
        bind_values = list(dict_likv.values())
        bind_names = [":" + str(i + 1) for i in range(len(bind_values))]
        curs = self.conn.cursor()
        sql_query = '''
                       select max(json_value(SPECIFIC_DATA, '$.tiaUser')) keep (dense_rank last order by LAST_UPDATE)
                       from ROBOT_QUEUE
                       where STATUS = 'SUCCESS' and BUSSINES_EXCEPT is null 
                         and PROCESS_NAME = 'Celni_Skla' 
                         and json_value(SPECIFIC_DATA, '$.PU') is null
                         and json_value(SPECIFIC_DATA, '$.tiaUser') in (%s) 
                    '''  % (",".join(bind_names))                
        curs.execute(sql_query, bind_values)       
        rows = curs.fetchall()
        if len(rows)>0:
            userId = rows[0][0]
        else:
            userId = None
        curs.close()
        return userId             


    def eA_Claims_getLastAssignUser(self):
        """ vyhledá poslední škodu, která byla přidělena uživateli a vrátí TIAid tohoto uživatele"""
        curs = self.conn.cursor()
        # sql_query = '''select SPECIFIC_DATA
        #                 from ROBOT_QUEUE
        #                 where PROCESS_NAME = 'EA_CLAIMS' and 
        #                       PROCESS_BRANCH = 'ClaimToReassign' and
        #                       not SPECIFIC_DATA like '%"AssignUserTIAId": ""%' and
        #                       LAST_UPDATE = (select max(LAST_UPDATE)
        #                                      from ROBOT_QUEUE
        #                                      where not SPECIFIC_DATA like '%"AssignUserTIAId": ""%' and
        #                                            PROCESS_NAME = 'EA_CLAIMS' and 
        #                                            PROCESS_BRANCH = 'ClaimToReassign'
        #             ''' 
        
        sql_query = '''select SPECIFIC_DATA
                       from ROBOT_QUEUE
                       where ID = (select max(ID)
                           from ROBOT_QUEUE
                           where not SPECIFIC_DATA like '%"AssignUserTIAId": ""%' and
                               PROCESS_NAME = 'EA_CLAIMS' and 
                               PROCESS_BRANCH = 'ClaimToReassign'and
                               not STATUS = 'SUSPENDED' )
                   '''
        curs.execute(sql_query)                            
        rows = curs.fetchall()
        if len(rows)>0:
            userId = json.loads(rows[0][0]).get('AssignUserTIAId')
        curs.close()
        return  userId     
        
    def eA_Claims_getEAclaimNum(self, TIAClaimNo):
        """ vyhledá EA id škody, pro číslo PU """
        eaClaimNo = ''
        curs = self.conn.cursor()
        sql_query = '''select NUMEACLAIM
                        from EA_REGISTRATION_DATA
                        where TIA_CASE_NO = :tiaClaimNo and REPORTED = (select min (REPORTED)
                                                                        from EA_REGISTRATION_DATA
                                                                        where TIA_CASE_NO = :tiaClaimNo)
                    ''' 
        curs.execute(sql_query,{'tiaClaimNo':TIAClaimNo} )                            
        rows = curs.fetchall()
        
        if len(rows)>0:
            eaClaimNo = rows[0][0]
        curs.close()
        return  eaClaimNo   
    def eA_Claims_getLastClaimAssignUser(self, claimNo):
        """ vyhledá posledního uživatele, kterému byla úspěšně přidělena vybraná škoda nebo vyhledá posledního uživatele, který dostal UZ škodu"""
        claim = '%'+claimNo+'%'
        userId = ''
        curs = self.conn.cursor()
        sql_query = '''select SPECIFIC_DATA
                        from ROBOT_QUEUE
                        where PROCESS_NAME = 'EA_CLAIMS' and 
                              PROCESS_BRANCH like '%Reassign%' and
                              not SPECIFIC_DATA like '%"AssignUserTIAId": ""%' and
                              reference like :claim
                    ''' 
        curs.execute(sql_query, {'claim':claim})
        rows = curs.fetchall()
        if len(rows)>0:
            userId = json.loads(rows[0][0]).get('AssignUserTIAId')
        curs.close()
        return userId
    def eA_Claims_getLastUZClaimUser(self):   
        userId = ''
        curs = self.conn.cursor()
        sql_query = '''select SPECIFIC_DATA
                        from ROBOT_QUEUE
                        where STATUS = 'SUCCESS' and
                              PROCESS_NAME = 'EA_CLAIMS' and 
                              PROCESS_BRANCH = 'UZClaimToReassign' and
                              LAST_UPDATE = (select max(LAST_UPDATE)
                                             from ROBOT_QUEUE
                                             where STATUS = 'SUCCESS' and
                                                   PROCESS_NAME = 'EA_CLAIMS' and 
                                                   PROCESS_BRANCH = 'UZClaimToReassign')
                    ''' 
        curs.execute(sql_query)                            
        rows = curs.fetchall()
        if len(rows)>0:
            userId = json.loads(rows[0][0]).get('AssignUserTIAId')
        curs.close()
        return  userId   
#
#def check_config_files():
#    
#    path = "\\\\sfsl02\\dokumenty\\RPA_LPU\\_CONFIG\\Robot_Overseer\\"
#    NumOfRobotsForQueue = "NumOfRobotsForQueue.json"
#    QueuesTiming = "QueuesTiming.json"
#    robot_skills = "robot_skills_2.json"
#    QueuesToSkip = "QueuesToSkip.csv"
#      
#    with open(os.path.join(path,NumOfRobotsForQueue)) as numforq:
#        NumOfRobotsForQueue = json.load(numforq)  
#        
#    with open(os.path.join(path,QueuesTiming)) as queuestiming:
#        QueuesTiming = json.load(queuestiming)  
#        
#    with open(os.path.join(path,robot_skills)) as robotskills:
#        robot_skills = json.load(robotskills)  
#        
##    u = os.path.join(path,robot_skills)
    def get_cred (self, process_name):
        """ Funkce pro načtení credentials z databáze, jako vstupní argument string názvu procesu. 
        Vrátí tuple (user,password)"""
        # print(connection_string)
        # print(process_name)
        # print(os.environ['USERNAME'])
        curs = self.conn.cursor()
        #print(self.connection_string)
        sql_query = """ select PASSWORD,TIA_LOGIN from ROBOT_CR where PROCESS= :PROCESS_NAME and ROWNUM = 1"""
        
        curs.execute(sql_query, {'PROCESS_NAME':process_name})            
        rows = curs.fetchall()
        try:
            user = rows[0][1]
            password = rows[0][0]
        except:
            user = ''
            password = ''
        password = password.encode('utf-8')    
        password = base64.b64decode(password).decode('utf-8')
        curs.close()
        return  user, password
    
    def get_cred_by_TIA_login (self, tia_login):
        """ Funkce pro načtení credentials z databáze, jako vstupní argument string názvu procesu. 
        Vrátí tuple (user,password)"""
        # print(connection_string)
        # print(process_name)
        # print(os.environ['USERNAME'])
        curs = self.conn.cursor()
        #print(self.connection_string)
        sql_query = """ select PASSWORD,TIA_LOGIN from ROBOT_CR where TIA_LOGIN= :tia_login and ROWNUM = 1"""
        
        curs.execute(sql_query, {'tia_login':tia_login})            
        rows = curs.fetchall()
        try:
            user = rows[0][1]
            password = rows[0][0]
        except:
            user = ''
            password = ''
        password = password.encode('utf-8')    
        password = base64.b64decode(password).decode('utf-8')
        curs.close()
        return  user, password
    
    
    def check_robot_restart(self, robotId = None, restart = None):
        """ Automatický restart dle nastavení sloupce RESTART v tabulkách PROCESS_SETUP nebo ROBOT_OVERSEER_WORK
            Hodnota R - restart PC, hodnota Y - restart pythonu (MasterBlaster.py), hodnota ? - pouze info přes return 
            Požadavek na restart lze nastavit i do parametru restart bez ohledu na to, co je v DB """
        if robotId:
            print('Check restart' + ('/T' + robotId[9:])) 
            curs = self.conn.cursor()
            
            # Pokud má proces nastavený restart, přeneseme je na roboty, který umí stejný process
            sql_query = """update ROBOT_OVERSEER_WORK
                           set RESTART = (select min(p.RESTART)
                                          from ROBOT_OVERSEER_WORK r, PROCESS_SETUP p 
                                          where p.RESTART in ('Y','R') 
                                            and r.ALLOWED ='Y'
                                            and upper(r.ROBOT_USER) like 'TECHROBOT%'
                                            and regexp_like(r.SKILLS, '\W' || p.PROCESS_NAME || '\W'))
                           where ROBOT_USER in (
                               select ROBOT_USER
                               from ROBOT_OVERSEER_WORK r, PROCESS_SETUP p 
                               where p.RESTART in ('Y','R') 
                                 and r.ALLOWED ='Y'
                                 and upper(r.ROBOT_USER) like 'TECHROBOT%'
                                 and regexp_like(r.SKILLS, '\W' || p.PROCESS_NAME || '\W'))
                        """
            curs.execute(sql_query)
            self.conn.commit()
            sql_query = """update PROCESS_SETUP set RESTART = 'N' where RESTART in ('Y','R')"""
            curs.execute(sql_query)
            self.conn.commit()
                
            # zjistíme, zda se má aktuální robot restartovat
            sql_query = """select RESTART from ROBOT_OVERSEER_WORK where upper(ROBOT_USER) = upper(:robotId) and RESTART in ('Y','R')"""
            curs.execute(sql_query, {'robotId': robotId})
            rows = curs.fetchall()   
            
            only_info = restart == '?'
            if only_info:
                restart = None
                
            # pokud je robot nalezen, tak změním RESTART = 'N' a stroj restartujeme
            if rows and not restart:
                restart = rows[0][0]
            if restart:
                if only_info:
                    return restart

                sql_query = """update ROBOT_OVERSEER_WORK set RESTART = 'N' where upper(ROBOT_USER) = upper(:robotId)"""
                curs.execute(sql_query, {'robotId': robotId})
                self.conn.commit()
                curs.close()
                
                if restart == 'R':
                    self.update_robot_work(robotId = robotId, transactionItem = 'Restarting Robot...', process_name = 'Master_Bot', rq_id = None, level = 1)
                    self.update_robot_work(robotId = robotId, transactionItem = '', process_name = 'Master_Bot', rq_id = None, level = 2)
                    os.system('shutdown -t 0 -r -f')
                    time.sleep(15)
                elif restart == 'Y':
                    self.update_robot_work(robotId = robotId, transactionItem = 'Restarting Python...', process_name = 'Master_Bot', rq_id = None, level = 1)
                    self.update_robot_work(robotId = robotId, transactionItem = '', process_name = 'Master_Bot', rq_id = None, level = 2)
                    print('Restarting Python...')
                    exit()
            else:
                curs.close()
        
    def set_robot_restart(self, robotId = None, restart = 'Y'):
        """ Nastaví sloupec RESTART v tabulce ROBOT_OVERSEER_WORK 
            Hodnota R - restart PC, hodnota Y - restart pythonu (výchozí) """
        if robotId and restart in ('Y','R'):
            curs = self.conn.cursor()
            
            sql_query = """update ROBOT_OVERSEER_WORK
                           set RESTART = :restart
                           where ROBOT_USER = :robotId
                        """
            curs.execute(sql_query, {'robotId': robotId, 'restart': restart})
            self.conn.commit()
            curs.close()
        
# kontroluje zda je v ROW tabulce a PROCESS_SETUP tabulce allowed Y, pokud ano, vrací True jinak False
    def robot_run_permitted(self, robot, process_name):
        if process_name != 'Master_Bot':
            curs = self.conn.cursor()
            sql_query = f"""select ALLOWED || nvl(RESTART,'N') from ROBOT_OVERSEER_WORK where ROBOT_USER = '{robot}'
                            """
            curs.execute(sql_query)
            rows = curs.fetchall()
            robot_al = rows[0][0]
            print(robot_al)
            curs.close()
        else:
            robot_al = "YN"
        curs = self.conn.cursor()
        sql_query = f"""select ALLOWED || nvl(RESTART,'N') from PROCESS_SETUP where PROCESS_NAME = '{process_name}'
                        """
        curs.execute(sql_query)
        rows = curs.fetchall()
        proc_al_al = rows[0][0]
        print(proc_al_al)
        curs.close()

        if robot_al == "YN" and proc_al_al == "YN":
            return(True)
        else:
            return(False)
   
            
        
        
# kontroluje zda je daný process_name ve skillech daného techrobota
    def robot_skill(self, robot, process_name):
        """ funkce ověřuje za VDI umí zpracovat daný typ úlohy, v tabulce Robot_over_seer kontroluje process_name oproti vydefinovaným skillům"""
        curs = self.conn.cursor()
        sql_query = f"""select SKILLS from ROBOT_OVERSEER_WORK where ROBOT_USER = '{robot}'
                        """
        curs.execute(sql_query)
        rows = curs.fetchall()
#            print(sql_query)
#            print(str(rows))
#            print(str(self.connection_string))
        rows = rows[0][0]
        curs.close()
        if rows is not None:
            skills = rows.replace("[", "").replace("]", "").replace(" ", "").split(",")
            
            for skill in skills:
                # print(process_name)
                # print(skill)
                if process_name == skill:
                    return(True)
            return(False)
        else:
            return(False)
        
#             kontroluje počet aktuálně běžících procesů z ROW tabulky a porovnává ho s povoleným počtem
    def check_process_count(self, process_name):
        """ funkce ověřuje počet běžících procesů v tabulce Robot_over_seer oproti počtu povolených robotů v process setup tabulce"""
        
        curs = self.conn.cursor()
        # selekt, který ověří, jestli robot a proces už v tabulce je, pokud ano tak allowed_cnt = int(rows[0][0]) pokud ne tak allowed_cnt = int(rows[0][0])-1
        sql_query = """ 
        select count(PROCESS_NAME) from ROBOT_OVERSEER_WORK where PROCESS_NAME = :process_name and not ROBOT_USER = :robot
                        """
        curs.execute(sql_query, {'process_name' : process_name,
                                 'robot' : os.environ['USERNAME']})
        
        rows = curs.fetchall()
        running_cnt = rows[0][0]
        curs.close()
        curs = self.conn.cursor()
        sql_query = """ 
        select MAX_ROBOTS from PROCESS_SETUP where PROCESS_NAME =:process_name
                        """
        curs.execute(sql_query,{'process_name':process_name})
        rows = curs.fetchall()
        curs.close()
        try:
            allowed_cnt = int(rows[0][0])
        except:
            raise Exception("Value in PROCESS_SETUP table is empty for process_name: "+str(process_name))
            return()
        print("running_cnt:" +str(running_cnt))
        print("allowed_cnt:" +str(allowed_cnt))
        if running_cnt < allowed_cnt:
                return True
        else:
            return False
        
    def check_items_count(self, process_name, in_progres = False, postponed = False):
        """ Vrátí počet itemů v robot_queue se statusem NEW"""
        curs = self.conn.cursor()
        if not in_progres and not postponed:
            sql_query = """ select count (ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = :process_name 
                              and STATUS = 'NEW'
                              and ITEM_PRIORITY > 0
                              and nvl(STARTED, sysdate) <= sysdate
                            """
        elif in_progres and not postponed:
            sql_query = """select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = :process_name 
                              and (STATUS = 'NEW' or (STATUS = 'IN PROGRESS' and (LAST_UPDATE < SYSDATE - INTERVAL '1' HOUR)))
                              and ITEM_PRIORITY > 0
                              and nvl(STARTED, sysdate) <= sysdate
                        """
        elif not in_progres and postponed:
            sql_query = """select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = :process_name 
                              and (STATUS = 'NEW' or (STATUS = 'POSTPONED' and (LAST_UPDATE < SYSDATE - INTERVAL '1' HOUR)))
                              and ITEM_PRIORITY > 0
                              and nvl(STARTED, sysdate) <= sysdate
                        """
          
        elif in_progres and postponed:
            sql_query = """select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = :process_name 
                              and (STATUS = 'NEW' or (STATUS in ('IN PROGRESS', 'POSTPONED') and (LAST_UPDATE < SYSDATE - INTERVAL '1' HOUR)))
                              and ITEM_PRIORITY > 0
                              and nvl(STARTED, sysdate) <= sysdate
                        """

        curs.execute(sql_query,{'process_name':process_name})
        rows = curs.fetchall()
        items_count = rows[0][0]
        curs.close()
        return items_count
        
    def set_cred (self, process_name, user, password,username):
        "uloží encryptovaný pass pro uvedený proces a username do tabulky robot_cr"
        encodedpass = base64.b64encode(bytes(password, 'utf-8'))
        pas_toDTB = encodedpass.decode('utf-8')
        data = {'userName':username,
                'password': pas_toDTB,
                'process': process_name,
                'tia_login': user
                 }
        print(data)
        curs = self.conn.cursor()
        sql_query = """ insert into ROBOT_CR (USERNAME, PASSWORD, PROCESS, TIA_LOGIN) values (:userName, :password, :process, :tia_login)"""
        curs.execute(sql_query, data)            
        self.conn.commit()
        curs.close()
    def update_cred (self, TIA_LOGIN, PASSWORD):
        """ Funkce pro update passwordu z databáze ROBOT_CR, jako vstupní argument string názvu login (TIA_LOGIN) a novy password. 
        Vrátí tuple (user,password)"""
        # print(connection_string)
        # print(process_name)
        # print(os.environ['USERNAME'])
        encodedpass = base64.b64encode(bytes(PASSWORD, 'utf-8'))
        pas_toDTB = encodedpass.decode('utf-8')
        curs = self.conn.cursor()
        sql_query = """ UPDATE ROBOT_CR
        SET PASSWORD = :pas_toDTB,
        LAST_UPDATE = :now
        WHERE TIA_LOGIN = :TIA_LOGIN"""
        
        curs.execute(sql_query,
                     pas_toDTB = pas_toDTB,
                     now = datetime.now(),
                     TIA_LOGIN = TIA_LOGIN
                     )            
        self.conn.commit()
        curs.close()
        print("credentials updated")
    
# kontroluje povolené intervaly práce pro jednotlivé process_names
        
    def check_timing(self, process_name):
        """ pro jednotlivé process_name kontroluje časy, kdy robot pracuje"""
        curs = self.conn.cursor()
        
        sql_query = f""" 
        select TIMING from PROCESS_SETUP where PROCESS_NAME ='{process_name}'
                        """
        curs.execute(sql_query)
        rows = curs.fetchall()
       
        try:
            timing = rows[0][0]
            if timing is None or timing == "":
                return(True)
            timing = timing.split("-")
        except:
            raise Exception("TIMING in PROCESS_SETUP table is empty for process_name: "+str(process_name))
 
        
        start_time_date = datetime.strptime(timing[0], '%H:%M')
        stop_time_date = datetime.strptime(timing[1], '%H:%M')
        sys_now = (datetime.now()).time()
        start_time = start_time_date.time()
        stop_time = stop_time_date.time()
        curs.close()
        if sys_now > start_time and sys_now < stop_time:
            return(True)
        else:
            return(False)
               
    def update_proces_setup(self, process_name, **columns):
        """ funkce pro update hodnot v tabulce process_setup
            update statusů toho co robot zrovna dělá"""
        # get connection string for DEV/PROD
        # print (connection_string)
        curs = self.conn.cursor()
        for column in columns.items():
            # ORACLE validace názvu cílového sloupce   - ochrana proti sql injection

            try:
                column_name = curs.callfunc('sys.dbms_assert.qualified_sql_name', oracledb.DB_TYPE_VARCHAR, [column[0]])
            except oracledb.DatabaseError:
                print('Invalid columnName')

                #  todo nevím jestli nedodělat nějaký warning nebo BREAK??
                continue
            statement = """ UPDATE process_setup SET {columnName} = :col_var
                            WHERE PROCESS_NAME = :process_name
                            """
            curs.execute(statement.format(columnName = column_name),
                          {'process_name':process_name,
                          'col_var':column[1]}
                          )
            self.conn.commit()
        curs.close()
            
            
    def perform_checks_MZ(self, process_name, robot=os.environ['USERNAME']):
        """ funkce provede kontrolu na robot skill, allowed, robot max count a timing, vstupem jsou stringy robot (os.environ['USERNAME']) a process_name """
        print("***")
        
        print("Starting check for process_name: "+str(process_name))
        print("Robot: " + str(os.environ['USERNAME']))
        print("***")
        print("ROBOT_SKILL_CHECK: "+str(self.robot_skill(robot, process_name)))
        print("TIMING_CHECK: "+str(self.check_timing(process_name)))
        print("PERMISSION_CHECK: "+str(self.robot_run_permitted(robot, process_name)))
        print("PROCESS_COUNT_CHECK: "+str(self.check_process_count(process_name)))
        print("***")
        
        if self.robot_skill(robot, process_name) and self.check_timing(process_name) and self.robot_run_permitted(robot, process_name) and self.check_process_count(process_name):
            return True
        else:
            return False
               
        
    def perform_checks(self, process_name, robot = os.environ['USERNAME']):
        """ funkce provede kontrolu na robot skill, allowed, robot max count, timing a time_for_run, 
            vstupem jsou stringy robot (os.environ['USERNAME']) a process_name """
        curs = self.conn.cursor()
        sql_query = """
                          select 
                            case 
                              when p.PROCESS_NAME in ('Master_Bot', 'Master_Man') then 'Y'
                              when regexp_like(r.SKILLS, '\W' || regexp_substr(p.PROCESS_NAME, '(.*?)(_L)*$',1,1,'',1) || '\W') then 'Y'
                              else 'N'
                            end SKILL,
                            p.TIMING TIMING_TEXT,
                            case 
                              when to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(p.TIMING, '^\d\d:\d\d'), to_char(sysdate, 'HH24:MI')) 
                                                                   and nvl(regexp_substr(p.TIMING, '\d\d:\d\d$'), to_char(sysdate, 'HH24:MI'))
                              then 'Y' 
                              else 'N' 
                            end TIMING,
                            to_char(p.TIME_FOR_RUN,'DD.MM.YYYY HH24:MI:SS') TIME_FOR_RUN_TEXT,
                            case 
                              when nvl(p.TIME_FOR_RUN, sysdate) <= sysdate
                              then 'Y'
                              else 'N'
                            end TIME_FOR_RUN,
                            case 
                              when (r.ALLOWED = 'Y' and nvl(r.RESTART,'N') = 'N' and p.ALLOWED = 'Y' and nvl(p.RESTART,'N') = 'N') or p.PROCESS_NAME = 'Master_Bot'
                              then 'Y' 
                              else 'N' 
                            end PERMISSION,
                            to_number(nvl(p.MAX_ROBOTS, '99')) MAX_ROBOTS,
                            (select count(1) from ROBOT_OVERSEER_WORK 
                              where (PROCESS_NAME = p.PROCESS_NAME and LASTUPDATE > sysdate - 1
                                  or PROCESS_NAME2 = p.PROCESS_NAME and LASTUPDATE2 > sysdate - 1)
                                and not ROBOT_USER = r.ROBOT_USER 
                                and SKILLS like '%Master_Bot%'
                                and upper(ROBOT_USER) like 'TECHROBOT%'
                                and ALLOWED = 'Y') CNT_ROBOTS,
                            case 
                              when nvl(pmb.TIME_FOR_RUN, sysdate) >= sysdate 
                              then 'Y' 
                              else 'N' 
                            end LOADERS_OK,
                            nvl(pmb.ALLOWED, 'Y') MB_PERMISSION
                          from ROBOT_OVERSEER_WORK r, PROCESS_SETUP p, PROCESS_SETUP pmb
                          where p.PROCESS_NAME = :process_name
                            and r.ROBOT_USER = :robot
                            and pmb.PROCESS_NAME = 'Master_Bot'
                    """
        curs.execute(sql_query, {'process_name' : process_name, 'robot' : robot}) 
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        perfcheck = curs.fetchall()
        curs.close()
        
        if perfcheck:
            perfcheck = perfcheck[0]
            err_text = ''
            if perfcheck.get('MB_PERMISSION') == 'N':
                err_text = 'MASTER_BOT PERMISSION: N, '
            else:
                if perfcheck.get('SKILL') == 'N':
                    err_text += 'SKILL: N, '
                if perfcheck.get('PERMISSION') == 'N':
                    # if process_name != 'Celni_Skla_AfterCalc':
                    err_text += 'PERMISSION: N, '
                if perfcheck.get('TIMING') == 'N':
                    err_text += 'TIMING: ' + str(perfcheck.get('TIMING_TEXT')) + ', '
                if perfcheck.get('TIME_FOR_RUN') == 'N':
                    err_text += 'TIME_FOR_RUN: ' + str(perfcheck.get('TIME_FOR_RUN_TEXT')) + ', '
                if perfcheck.get('CNT_ROBOTS') >= perfcheck.get('MAX_ROBOTS'):
                    err_text += 'PROCESS_COUNT: ' + str(perfcheck.get('CNT_ROBOTS')) + '/' + str(perfcheck.get('MAX_ROBOTS')) + ', '
                if perfcheck.get('LOADERS_OK') == 'N':
                    err_text += 'LOADERS_OK: N, '
        else:
            err_text = 'Unknown process or robot  '
            
        print ('Check ' + process_name + ('/T' + robot[9:] if robot.upper().startswith('TECHROBOT') else '/' + robot) + ': ' + ('OK' if err_text == '' else 'NOK'))
        if err_text != '':
            print('>>>>> ' + err_text[:-2])
            return False
        else:
            return True


    def perform_checks_all(self, robot = os.environ['USERNAME']):
        """ funkce vytvoří list procesů, které mohou být aktuálně na daném robotovi spuštěny
            - provede kontrolu na robot skill, allowed, robot max count, timing a time_for_run, 
            vstupem je jméno robota (os.environ['USERNAME']) """
        curs = self.conn.cursor()
        sql_query = """
                    with chck as (
                          select 
                            p.PROCESS_NAME,
                            nvl(p.lvl,'1') LVL,
                            case 
                              when p.PROCESS_NAME in ('Master_Bot', 'Master_Man') then 'Y'
                              when regexp_like(r.SKILLS, '\W' || regexp_substr(p.PROCESS_NAME, '(.*?)(_L)*$',1,1,'',1) || '\W') then 'Y'
                              else 'N'
                            end SKILL,
                            p.TIMING TIMING_TEXT,
                            case 
                              when to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(p.TIMING, '^\d\d:\d\d'), to_char(sysdate, 'HH24:MI')) 
                                                                   and nvl(regexp_substr(p.TIMING, '\d\d:\d\d$'), to_char(sysdate, 'HH24:MI'))
                              then 'Y' 
                              else 'N' 
                            end TIMING,
                            to_char(p.TIME_FOR_RUN,'DD.MM.YYYY HH24:MI:SS') TIME_FOR_RUN_TEXT,
                            case 
                              when nvl(p.TIME_FOR_RUN, sysdate) <= sysdate
                              then 'Y'
                              else 'N'
                            end TIME_FOR_RUN,
                            case 
                              when (r.ALLOWED = 'Y' and nvl(r.RESTART,'N') = 'N' and p.ALLOWED = 'Y' and nvl(p.RESTART,'N') = 'N') or p.PROCESS_NAME = 'Master_Bot'
                              then 'Y' 
                              else 'N' 
                            end PERMISSION,
                            
                            to_number(nvl(p.MAX_ROBOTS, '99')) MAX_ROBOTS,

                            (select count(1) from ROBOT_OVERSEER_WORK 
                              where (PROCESS_NAME = p.PROCESS_NAME and LASTUPDATE > sysdate - 1
                                  or PROCESS_NAME2 = p.PROCESS_NAME and LASTUPDATE2 > sysdate - 1)
                                and not ROBOT_USER = r.ROBOT_USER 
                                and SKILLS like '%Master_Bot%'
                                and upper(ROBOT_USER) like 'TECHROBOT%'
                                and ALLOWED = 'Y') CNT_ROBOTS,
  
                            (select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = p.PROCESS_NAME 
                              and (STATUS = 'NEW') 
                              and ITEM_PRIORITY > 0 
                              and nvl(STARTED, sysdate) <= sysdate) CNT_NEW,
                            
                            (select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = p.PROCESS_NAME 
                              and (STATUS = 'NEW' or (STATUS = 'IN PROGRESS' and (LAST_UPDATE < sysdate - INTERVAL '1' HOUR))) 
                              and ITEM_PRIORITY > 0 
                              and nvl(STARTED, sysdate) <= sysdate) CNT_NEW_INP,

                            (select count(ID)
                            from ROBOT_QUEUE
                            where PROCESS_NAME = p.PROCESS_NAME 
                              and (STATUS = 'NEW' or (STATUS in ('IN PROGRESS','POSTPONED') and (LAST_UPDATE < sysdate - INTERVAL '1' HOUR))) 
                              and ITEM_PRIORITY > 0 
                              and nvl(STARTED, sysdate) <= sysdate) CNT_NEW_INP_POS

                          from ROBOT_OVERSEER_WORK r, PROCESS_SETUP p, PROCESS_SETUP pmb
                          where r.ROBOT_USER = :robot
                            and pmb.PROCESS_NAME = 'Master_Bot'
                            and nvl(pmb.ALLOWED, 'Y') = 'Y'
                            and r.SKILLS like '%Master_Bot%'
                    )
                    select 
                      chck.*,
                      case when skill = 'Y'
                            and timing = 'Y'
                            and time_for_run = 'Y'
                            and permission = 'Y'
                            and cnt_robots < max_robots
                           then 'OK' 
                           else 'NOK'
                      end STATUS,
                      case when skill <> 'Y' then 'SKILL: N, ' end ||
                      case when timing <> 'Y' then 'TIMING: N (' || TIMING_TEXT || '), ' end ||
                      case when time_for_run <> 'Y' then 'TIME_FOR_RUN: N (' || TIME_FOR_RUN_TEXT || '), ' end ||
                      case when permission <> 'Y' then 'PERMISION: N, ' end ||
                      case when cnt_robots >= max_robots then 'PROCESS COUNT: '  || CNT_ROBOTS || '/' || MAX_ROBOTS || ', ' end REASON
                    from chck
                    """
        curs.execute(sql_query, {'robot' : robot}) 
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        perfcheck = curs.fetchall()
        curs.close()
        return perfcheck
        
        
    def check_new_for_skipping(self, process_name):
        """ funkce, která prověřuje zda jsou nějaké itemy ve stavu NEW před uploadem nového filu, pokud ano, přepne status na SUCCESS a BUSINNESS_EXCEPTION na skipped_because_of new_file"""
        curs = self.conn.cursor()
        sql_query = f""" 
        select count(id) from ROBOT_QUEUE where PROCESS_NAME ='{process_name}' and STATUS = 'NEW'
                        """
        curs.execute(sql_query)
        rows = curs.fetchall()
        new_count = rows[0][0]
        curs.close()
        if new_count > 0:
            print("Items to be skipped")
            curs = self.conn.cursor()
            sql_query = f""" UPDATE ROBOT_QUEUE 
            SET STATUS = 'SUCCESS', BUSSINES_EXCEPT = 'skipped_because_of new_file'
            WHERE PROCESS_NAME = '{process_name}' and STATUS = 'NEW' """
            curs.execute(sql_query)            
            self.conn.commit()
            curs.close()
            print("Items_skipped")
  
        else:
            print("No items to be skipped")
            pass
        
    def get_time_for_run(self,process_name):
            curs = self.conn.cursor()
            
            sql_query = """ 
                            select TIME_FOR_RUN from PROCESS_SETUP where PROCESS_NAME =:process_name
                            """
            curs.execute(sql_query, {'process_name':process_name})
            rows = curs.fetchall()
            last_run = rows[0][0]
            if last_run is None:
                 with oracledb.connect(self.connection_string) as conn:
                    curs = conn.cursor()
                    sql_query = sql_query = """ update PROCESS_SETUP set TIME_FOR_RUN = :now where process_name = :process_name"""
           
                    curs.execute(sql_query
                                 , 
                                 {'now': datetime.now(),
                                 'process_name': process_name
                                 })   
                    self.conn.commit()
                    x = datetime.now()+timedelta(hours=int(1))
                    curs.close()
                    return (x)
                # 
            else:
                return last_run


    def update_time_for_run(self, process_name, time_for_run, force = False):
        """ funkce aktualizuje time_for_run, pokud je time_for_run < vstupní parametr time_for_run
            pokud je nastaveno force = True, pak je time_for_run přepsáno vždy
            vrací True, pokud dojde k aktualizaci, jinak False """
        curs = self.conn.cursor()
        sql_query = """
                          update PROCESS_SETUP set TIME_FOR_RUN = :time where PROCESS_NAME = :process_name and (nvl(TIME_FOR_RUN, sysdate) <= sysdate or :force = 'True')
                    """
        curs.execute(sql_query, {'process_name': process_name, 'time': time_for_run, 'force': str(force)}) 
        self.conn.commit()
        update = bool(curs.rowcount)
        curs.close()
        return update
            
    
    # TODO  Zkontrolovat, že se tato metoda nepoužívá a zrušit
    def get_last_assigned_user_index_EA_CLAIMS(self,teamLeaders_len):
        index_cnt = teamLeaders_len -1
        curs = self.conn.cursor()
        sql_query = """select counter from process_setup  where process_name = 'EA_CLAIMS' for update"""
        curs.execute(sql_query)
        rows = curs.fetchall()[0][0]
        if rows >= index_cnt:
            new_index = 0
        else:
            new_index = rows+1
        self.update_process_count('EA_CLAIMS', new_index)
        return rows
    
    # process_name = 'Celni_Skla'
    # teamLeaders_len = len(dict_likv)
    # curs = db.conn.cursor()
    # db.update_process_count(process_name, new_index)
    
    def get_last_assigned_user_index(self, teamLeaders_len, process_name):
        index_cnt = teamLeaders_len - 1
        curs = self.conn.cursor()
        sql_query = f"""select counter from process_setup where process_name = '{process_name}' for update"""
        curs.execute(sql_query)
        rows = curs.fetchall()[0][0]
        if not rows:
            rows = 0
        if rows >= index_cnt:
            new_index = 0
        else:
            new_index = rows + 1
        self.update_process_count(process_name, new_index)
        return rows
    
    
    def update_process_count(self, process_name, counter):
        curs = self.conn.cursor()
        statement = """ UPDATE process_setup SET counter = :counter
                        WHERE process_name = :process_name
                        """
        curs.execute(statement,{'counter' : counter,
                                'process_name' : process_name})
        self.conn.commit()
        curs.close()
        return     
   
    def get_CTP_spec_data(self,reference, process_name): 
        """ vrací specific data z posledního ClaimToPay pro referenci, použití myšleno pro Claim to Close branch """
        curs = self.conn.cursor()
        sql_query = f"""select specific_data from robot_queue  where reference like '{reference}' and process_branch = 'ClaimToPay' and process_name = '{process_name}' order by last_update desc"""
        curs.execute(sql_query)
        try:
            rows = curs.fetchall()[0][0]
        except IndexError:
            rows = None
        if rows is not None:
            rows = json.loads(rows)
        else:
            rows = None
        return rows
    
    
    
    def get_db_creds(self,env = None, db = None):
        
        '''
        Parameters
        ----------
        env : str, povinný parametr
            prostředí 'PROD', 'DEV'. The default is None.
        db : str, povinný parametr
            Jméno databáze. The default is None.

        Returns
        -------
        creds

        '''
        curs = self.conn.cursor()
        sql_query = f"""select * from dtb_con  where db_name = '{db}'"""
        curs.execute(sql_query)
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        row  = rows[0]
        return row
    
    def ea_reg_in_insert (self,
                 reference = None,
                 in_file = None,
                 out_file = None):

        curs = self.conn.cursor()
      
        statement = """ INSERT INTO EA_REG_IN (REFERENCE, IN_FILE,OUT_FILE,LAST_UPDATE )
                                    VALUES (:reference, :in_file, :out_file, :last_update)
                                """
        curs.execute(statement,{'reference': reference,
                                'in_file': in_file, 
                                'out_file': out_file,
                                'last_update': datetime.now()})
        self.conn.commit()
        curs.close()   
        print(f'Data: {str(reference)}, {str(in_file)}, {str(out_file)}')     
        
        
    def ea_reg_validate_pu(self):
            curs = self.conn.cursor()
            sql_query = """select * from robot_queue 
                                where process_name = 'EA_CLAIMS_REG'
                                and status in ('SUCCESS','FAILED')
                                and LAST_UPDATE >= TRUNC(SYSDATE) - 5
                                and error_codes is null
                                order by LAST_UPDATE desc"""
                                
            curs.execute(sql_query)
            curs.rowfactory = makeDictFactory(curs)
            rows = curs.fetchall()
            return rows

    def get_params(self, key = None, process_name = None):
            my_list = []
            if key:
                my_list.append(f"key = '{key}'")          
            if process_name:
                my_list.append(f"process_name = '{process_name}'")
                
            my_condition = ' and '.join(my_list)   
            curs = self.conn.cursor()
            sql_query = f"""select * from PROCESS_PARAM 
                                where {my_condition}
                                """  
            # sql_query = f"""select * from PROCESS_PARAM 
            #                     where process_name = :process_name
            #                       and key = :key
            #                     """  
            # curs.execute(sql_query,{'process_name': process_name, 'key': key})
            curs.execute(sql_query)
            columns = [col[0] for col in curs.description]
            curs.rowfactory = lambda *args: dict(zip(columns, args))
            rows = curs.fetchall()
            return rows

    def get_process_param(self, process_name = None, key = None, subkey = None, default = None):
        curs = self.conn.cursor()
        sql_query = """ select value from PROCESS_PARAM 
                        where nvl(process_name, 'None') = :process_name
                          and key = :key
                    """  
        curs.execute(sql_query,{'process_name': str(process_name), 'key': str(key)})
        rows = curs.fetchall()
        if not rows:
            return default
        value = rows[0][0]
        try:
            val_json =  json.loads(value)  
            return val_json.get(subkey, default) if subkey else val_json
        except:
            return value        

    def get_seq_val(self, seq_name = None):
        curs = self.conn.cursor()
        statement = f"SELECT {seq_name}.nextval FROM dual"
        curs.execute(statement)
        rows = curs.fetchall()[0][0]
        curs.close()
        return rows  

    def master_man_OLD(self):
        """ Automaticky nastavuje počet max_robots procesů Master_Bota """
        
        curs = self.conn.cursor()
        sql_query = """with tab as ( -- zjištění doby trvání každého branche ze sledovaných procesů
                                    select 
                                      q.process_name,
                                      q.process_branch,
                                      nvl(avg(decode(q.status,'SUCCESS',ended - started + sysdate - sysdate,null)) *60*24, 10) prum --doba běhu
                                    from robot_queue q
                                    where q.process_name in (select process_name from process_sla)
                                      and q.status in ('SUCCESS','NEW')
                                      and q.creation_date > sysdate - 7
                                      and q.item_priority > 0
                                    group by q.process_name, q.process_branch  
                                   ),
                           tab2 as ( -- počet robotů, které jsou k dispozici
                                    select count(1) all_robots 
                                    from robot_overseer_work r
                                    where r.skills like '%Master_Bot%' 
                                      and r.lastupdate > sysdate - 1/24/60*30
                                      and r.allowed = 'Y'
                                      and upper(r.robot_user) like 'TECHROBOT%'
                                      and not (r.process_name = 'Master_Bot' and r.lastupdate < sysdate - 1/24/60*7)
                                   ),
                           tab3 as ( -- zjištění důležitosti (poměru) robotů na každý proces
                                    select 
                                      q.process_name,
                                      count(case when q.status in ('NEW','IN PROGRESS') and s.allowed = 'Y' and nvl(q.started,sysdate) <= sysdate 
                                                        and to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(s.TIMING, '^\d\d:\d\d'),to_char(sysdate, 'HH24:MI')) 
                                                                                            and nvl(regexp_substr(s.TIMING, '\d\d:\d\d$'),to_char(sysdate, 'HH24:MI')) 
                                                 then 1 
                                            end) poc,
                                      sum(case when q.status in ('NEW','IN PROGRESS') and s.allowed = 'Y' and nvl(q.started,sysdate) <= sysdate  
                                                        and to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(s.TIMING, '^\d\d:\d\d'),to_char(sysdate, 'HH24:MI')) 
                                                                                            and nvl(regexp_substr(s.TIMING, '\d\d:\d\d$'),to_char(sysdate, 'HH24:MI')) 
                                               then prum / greatest(to_number(q.creation_date + 1/24/60 * sla.sla_time - sysdate), 1/24/60/60)
                                               else 0 
                                          end) pomer,
                                      max(s.max_robots) max_robots,
                                      max(sla.limit_robots) limit_robots
                                    from robot_queue q, tab, process_setup s, process_sla sla    
                                    where q.process_name = tab.process_name
                                       and nvl(q.process_branch,1) = nvl(tab.process_branch,1)
                                       and q.status in ('NEW','IN PROGRESS','SUCCESS')
                                       and q.creation_date > sysdate - 7
                                       and q.process_name = s.process_name
                                       --and s.process_name in ('Celni_Skla','Celni_Skla_AfterCalc','EA_CLAIMS','EA_CLAIMS_GLI','PMV_AfterCalc','Dopisy_Setreni','MAJ_AfterCalc')
                                       and q.process_name = sla.process_name --(+)
                                       --and q.process_branch = nvl(sla.process_branch,q.process_branch)
                                       and not (q.status = 'IN PROGRESS' and q.id not in (select rq_id from robot_overseer_work))
                                       and q.item_priority > 0
                                    group by q.process_name
                                   )
                        select 
                          tab3.process_name,
                          to_number(nvl(tab3.max_robots,'0')) max_robots,
                          tab3.poc,
                          --sum(tab3.poc) over (partition by null) poc_celk,
                          sum(least(tab3.poc,nvl(tab3.limit_robots,tab3.poc))) over (partition by null) poc_celk,
                          tab3.pomer,
                          greatest(pomer, sum(tab3.pomer) over (order by pomer desc rows between current row and unbounded following)) pomerc,
                          tab2.all_robots,
                          least(nvl(tab3.limit_robots,tab2.all_robots),tab2.all_robots) limit_robots
                        from tab3, tab2  
                        union all
                        select sla.process_name, to_number(s.max_robots), 0, 0, 0, 0, tab2.all_robots,  least(nvl(sla.limit_robots,tab2.all_robots),tab2.all_robots) limit_robots
                        from process_setup s, process_sla sla , tab2
                        where sla.process_name = s.process_name
                          and sla.process_name not in (select process_name from tab3)
                        order by pomer desc
                    """
        curs.execute(sql_query) 
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows = curs.fetchall()
        curs.close()
        
        if rows:
            curs = self.conn.cursor()
            if rows[0].get('POC_CELK') > rows[0].get('ALL_ROBOTS'):
        
                rob = rows[0].get('ALL_ROBOTS')
                for _ in range(len(rows)):
                    dct = rows.pop(0)
                    if dct.get('POMERC') > 0:
                        pom = round(dct.get('POMER') / dct.get('POMERC') * rob)
                        if pom > dct.get('POC'):
                            pom = dct.get('POC')
                        if pom > dct.get('LIMIT_ROBOTS'):
                            pom = dct.get('LIMIT_ROBOTS')
                        rob -= pom
                    else:
                        pom = 0
                    dct['POMER_ROB'] = pom
                    rows.append(dct.copy())

                # Pokud zbyli roboti, přidej podle důležitosti procesu, který má více úloh než aktuálně přidělených robotů
                if rob > 0:
                    for _ in range(len(rows)):
                        dct = rows.pop(0)
                        if rob > 0 and dct.get('POMER_ROB') < dct.get('POC'):
                            pom = dct.get('POC') - dct.get('POMER_ROB')
                            if pom > rob:
                                pom = rob
                            if pom + dct['POMER_ROB'] > dct.get('LIMIT_ROBOTS'):
                                pom = dct.get('LIMIT_ROBOTS') - dct['POMER_ROB']    
                            rob -= pom    
                            dct['POMER_ROB'] += pom
                        rows.append(dct.copy())
        
                # Nastav vypočítaný poměr
                for dct in rows:
                    print(dct.get('PROCESS_NAME').ljust(21) + ': ' + str(dct.get('POC')).rjust(3) + '/' + str(dct.get('POC_CELK')) + ' --> ' + str(dct.get('POMER_ROB')).rjust(2) + '/' + str(dct.get('ALL_ROBOTS')))
                    if dct.get('POMER_ROB') != dct.get('MAX_ROBOTS'):
                        sql_query = """ update PROCESS_SETUP set MAX_ROBOTS = :max_robots where PROCESS_NAME = :process_name """
                        curs.execute(sql_query, {'process_name': dct.get('PROCESS_NAME'), 'max_robots': str(dct.get('POMER_ROB'))})
                        self.conn.commit()
                
            else:
                # Nastav stejný poměr pro všechny roboty
                for dct in rows:
                    print(dct.get('PROCESS_NAME').ljust(21) + ': ' + str(dct.get('POC')).rjust(3) + '/' + str(dct.get('POC_CELK')) + ' --> ' + str(dct.get('LIMIT_ROBOTS')).rjust(2) + '/' + str(dct.get('ALL_ROBOTS')))
                    if dct.get('LIMIT_ROBOTS') != dct.get('MAX_ROBOTS'):
                        sql_query = """ update PROCESS_SETUP set MAX_ROBOTS = :max_robots where PROCESS_NAME = :process_name """
                        curs.execute(sql_query, {'process_name': dct.get('PROCESS_NAME'),'max_robots': str(dct.get('LIMIT_ROBOTS'))})
                        self.conn.commit()    
     
            curs.close()

        
    def master_man(self):
        """ Automaticky nastavuje počet max_robots procesů Master_Bota """
        
        curs = self.conn.cursor()
        sql_query = """with tab as ( -- zjištění doby trvání každého branche ze sledovaných procesů
                                    select 
                                      q.process_name,
                                      q.process_branch,
                                      nvl(avg(decode(q.status,'SUCCESS',ended - started + sysdate - sysdate,null)) *60*24, 10) prum --doba běhu
                                    from robot_queue q
                                    where q.process_name in (select process_name from process_sla)
                                      and q.status in ('SUCCESS','NEW')
                                      and q.creation_date > sysdate - 7
                                      and q.item_priority > 0
                                    group by q.process_name, q.process_branch  
                                   ),
                           tab2 as ( -- počet robotů, které jsou k dispozici
                                    select 
                                       count(case when r.lastupdate > sysdate - 1/24/60*30 and not (r.process_name = 'Master_Bot' and r.lastupdate < sysdate - 1/24/60*7) then 1 end) all_robots, 
                                       count(case when r.lastupdate2 > sysdate - 1/24/60*30 and not (r.process_name2 = 'Master_Bot' and r.lastupdate2 < sysdate - 1/24/60*7) then 1 end) all_robots2 
                                    from robot_overseer_work r
                                    where r.skills like '%Master_Bot%' 
                                      and r.allowed = 'Y'
                                      and nvl(r.transactionitem,'x') <> 'Restarting Robot...'
                                      and upper(r.robot_user) like 'TECHROBOT%'
                                   ),
                           tab3 as ( -- zjištění důležitosti (poměru) robotů na každý proces
                                    select 
                                      q.process_name,
                                      max(nvl(s.lvl,'1')) lvl,
                                      count(case when q.status in ('NEW','IN PROGRESS') and s.allowed = 'Y' and nvl(q.started,sysdate) <= sysdate 
                                                        and to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(s.TIMING, '^\d\d:\d\d'),to_char(sysdate, 'HH24:MI')) 
                                                                                            and nvl(regexp_substr(s.TIMING, '\d\d:\d\d$'),to_char(sysdate, 'HH24:MI')) 
                                                 then 1 
                                            end) poc,
                                      sum(case when q.status in ('NEW','IN PROGRESS') and s.allowed = 'Y' and nvl(q.started,sysdate) <= sysdate  
                                                        and to_char(sysdate, 'HH24:MI') between nvl(regexp_substr(s.TIMING, '^\d\d:\d\d'),to_char(sysdate, 'HH24:MI')) 
                                                                                            and nvl(regexp_substr(s.TIMING, '\d\d:\d\d$'),to_char(sysdate, 'HH24:MI')) 
                                               then prum / greatest(to_number(q.creation_date + 1/24/60 * sla.sla_time - sysdate), 1/24/60/60)
                                               else 0 
                                          end) pomer,
                                      max(s.max_robots) max_robots,
                                      max(sla.limit_robots) limit_robots
                                    from robot_queue q, tab, process_setup s, process_sla sla    
                                    where q.process_name = tab.process_name
                                       and nvl(q.process_branch,1) = nvl(tab.process_branch,1)
                                       and q.status in ('NEW','IN PROGRESS','SUCCESS')
                                       and q.creation_date > sysdate - 7
                                       and q.process_name = s.process_name
                                       --and s.process_name in ('Celni_Skla','Celni_Skla_AfterCalc','EA_CLAIMS','EA_CLAIMS_GLI','PMV_AfterCalc','Dopisy_Setreni','MAJ_AfterCalc')
                                       and q.process_name = sla.process_name --(+)
                                       --and q.process_branch = nvl(sla.process_branch,q.process_branch)
                                       and not (q.status = 'IN PROGRESS' and q.id not in (select rq_id from robot_overseer_work))
                                       and q.item_priority > 0
                                    group by q.process_name
                                   )
                        select 
                          tab3.process_name,
                          lvl,
                          to_number(nvl(tab3.max_robots,'0')) max_robots,
                          tab3.poc,
                          --sum(tab3.poc) over (partition by null) poc_celk,
                          sum(least(tab3.poc,nvl(tab3.limit_robots,tab3.poc))) over (partition by lvl) poc_celk,
                          tab3.pomer,
                          greatest(pomer, sum(tab3.pomer) over (partition by lvl order by pomer desc rows between current row and unbounded following)) pomerc,
                          case when lvl = '1'
                            then tab2.all_robots
                            else tab2.all_robots2
                          end all_robots,
                          case when lvl = '1'
                            then least(nvl(tab3.limit_robots,tab2.all_robots),tab2.all_robots) 
                            else least(nvl(tab3.limit_robots,tab2.all_robots2),tab2.all_robots2) 
                          end limit_robots
                        from tab3, tab2  
                        union all                        
                        select sla.process_name, nvl(s.lvl,'1'), to_number(s.max_robots), 0, 0, 0, 0, tab2.all_robots, 
                          case when nvl(lvl,'1') = '1'
                            then least(nvl(sla.limit_robots,tab2.all_robots),tab2.all_robots) 
                            else least(nvl(sla.limit_robots,tab2.all_robots2),tab2.all_robots2)
                          end limit_robots
                        from process_setup s, process_sla sla , tab2
                        where sla.process_name = s.process_name
                          and sla.process_name not in (select process_name from tab3)
                        order by lvl, pomer desc
                    """
        curs.execute(sql_query) 
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows_all = curs.fetchall()
        curs.close()
        
        def set_robots(rows):
            if rows:
                curs = self.conn.cursor()
                if rows[0].get('POC_CELK') > rows[0].get('ALL_ROBOTS'):
            
                    rob = rows[0].get('ALL_ROBOTS')
                    for _ in range(len(rows)):
                        dct = rows.pop(0)
                        if dct.get('POMERC') > 0:
                            pom = round(dct.get('POMER') / dct.get('POMERC') * rob)
                            if pom > dct.get('POC'):
                                pom = dct.get('POC')
                            if pom > dct.get('LIMIT_ROBOTS'):
                                pom = dct.get('LIMIT_ROBOTS')
                            rob -= pom
                        else:
                            pom = 0
                        dct['POMER_ROB'] = pom
                        rows.append(dct.copy())
    
                    # Pokud zbyli roboti, přidej podle důležitosti procesu, který má více úloh než aktuálně přidělených robotů
                    if rob > 0:
                        for _ in range(len(rows)):
                            dct = rows.pop(0)
                            if rob > 0 and dct.get('POMER_ROB') < dct.get('POC'):
                                pom = dct.get('POC') - dct.get('POMER_ROB')
                                if pom > rob:
                                    pom = rob
                                if pom + dct['POMER_ROB'] > dct.get('LIMIT_ROBOTS'):
                                    pom = dct.get('LIMIT_ROBOTS') - dct['POMER_ROB']    
                                rob -= pom    
                                dct['POMER_ROB'] += pom
                            rows.append(dct.copy())
            
                    # Nastav vypočítaný poměr
                    for dct in rows:
                        print(dct.get('PROCESS_NAME').ljust(21) + ': ' + str(dct.get('POC')).rjust(3) + '/' + str(dct.get('POC_CELK')) + ' --> ' + str(dct.get('POMER_ROB')).rjust(2) + '/' + str(dct.get('ALL_ROBOTS')))
                        if dct.get('POMER_ROB') != dct.get('MAX_ROBOTS'):
                            sql_query = """ update PROCESS_SETUP set MAX_ROBOTS = :max_robots where PROCESS_NAME = :process_name """
                            curs.execute(sql_query, {'process_name': dct.get('PROCESS_NAME'), 'max_robots': str(dct.get('POMER_ROB'))})
                            self.conn.commit()
                    
                else:
                    # Nastav stejný poměr pro všechny roboty
                    for dct in rows:
                        print(dct.get('PROCESS_NAME').ljust(21) + ': ' + str(dct.get('POC')).rjust(3) + '/' + str(dct.get('POC_CELK')) + ' --> ' + str(dct.get('LIMIT_ROBOTS')).rjust(2) + '/' + str(dct.get('ALL_ROBOTS')))
                        if dct.get('LIMIT_ROBOTS') != dct.get('MAX_ROBOTS'):
                            sql_query = """ update PROCESS_SETUP set MAX_ROBOTS = :max_robots where PROCESS_NAME = :process_name """
                            curs.execute(sql_query, {'process_name': dct.get('PROCESS_NAME'),'max_robots': str(dct.get('LIMIT_ROBOTS'))})
                            self.conn.commit()    
         
                curs.close()

        print('-- level 1 --')
        rows = [r for r in rows_all if r.get('LVL') == '1']
        set_robots(rows)
        print('-- level 2 --')
        rows = [r for r in rows_all if r.get('LVL') == '2']
        set_robots(rows)

      
        
    def update_robot_work(self, robotId, **columns):
        """ funkce pro update hodnot v tabulce ROBOT_OVERSEER_WORK
            update statusů toho co robot zrovna dělá"""
        curs = self.conn.cursor()
        
        cols = {key.upper(): val for key, val in columns.items()}
        lvl = cols.pop('LEVEL', None)
        if not lvl:
            # Zjisti level z process_setup dle process_name
            process_name = cols.get('PROCESS_NAME', cols.get('PROCESS_NAME2'))
            sql_query = """ select nvl(lvl, nvl((select '2' from robot_overseer_work where robot_user = :robot_id and process_name2 = :process_name),'1')) 
                            from process_setup where process_name = :process_name
                        """
            curs.execute(sql_query, {'process_name': process_name, 'robot_id': robotId})
            rows = curs.fetchall()
            if not rows:
                lvl = '1'
            else:
                lvl = rows[0][0]
    
        columns = {}
        for key, val in cols.items():
            if str(lvl) == '1' and key in ('PROCESS_NAME2','LASTUPDATE2','TRANSACTIONITEM2','RQ_ID2'):
                columns[key[:-1]] = val
            elif str(lvl) == '2' and key in ('PROCESS_NAME','LASTUPDATE','TRANSACTIONITEM','RQ_ID'):
                columns[key + '2'] = val
            else:
                columns[key] = val
        
        if str(lvl) == '1':
            columns.setdefault('RQ_ID', None)
            columns.setdefault('LASTUPDATE', datetime.now())
        else:
            columns.setdefault('RQ_ID2', None)
            columns.setdefault('LASTUPDATE2', datetime.now())
            
        for column in columns.items():
            # ORACLE validace názvu cílového sloupce   - ochrana proti sql injection
            try:
                column_name = curs.callfunc('sys.dbms_assert.qualified_sql_name', oracledb.DB_TYPE_VARCHAR, [column[0]])
            except oracledb.DatabaseError:
                print('Invalid columnName')

                #  todo nevím jestli nedodělat nějaký warning nebo BREAK??
                continue
            statement = """ UPDATE robot_overseer_work SET {columnName} = :col_var
                            WHERE robot_user = :robot_id
                        """
            
            curs.execute(statement.format(columnName = column_name),
                          {'robot_id': robotId,
                           'col_var': column[1]}
                        )
            self.conn.commit()
        curs.close()
                
# MojeRpa_DB = Rpa_DB(env = 'PROD')
# MojeRpa_DB.get_params(key = 'dev_config')
# MojeRpa_DB.RQ_single_loader(rq_data)
# x = MojeRpa_DB.get_CTP_spec_data(8093609,'PMV_AfterCalc')

# rq_data = {'STATUS': 'NEW',
#             'REFERENCE' :  '123456789',
#             'ITEM_PRIORITY' : 99,
#             'PROCESS_NAME': 'EA_CLAIMS',
#             'CREATION_DATE' : datetime.now(),
#             'PROCESS_BRANCH' : 'ClaimToClose',
#             'SPECIFIC_DATA' : 'TEST',
#             'LAST_UPDATE' : None}

# xxx = MojeRpa_DB.get_cred('EA_CLAIMS')
# MojeRpa_DB.set_cred(process_name = 'TIA_FIC', username = 'Techrobot23', password = 'pRx88SDH', user='Techrobot23')
# Daneel1827
# MojeRpa_DB.update_cred(TIA_LOGIN = 'TTE450', PASSWORD = 'Daneel1967')
# MojeRpa_DB.get_cred('Celni_Skla_AfterCalc_zero_limit')
# encodedpass = base64.b64encode(bytes('Daneel1971', 'utf-8'))
# pas_toDTB = encodedpass.decode('utf-8')



class Ods_DB():
    """nemá dev prostředí"""   
    def __init__(self, config_yaml = None, env = 'PROD'):
        self.env = env
        oracle_path = os.path.join('c:' + os.sep,'Oracle','instantclient')
        ##### insert the path to the Oracle dll to the PATH variables#######
        path = os.environ['PATH']
        path_list = path.split(';')
        oraclecount = 0
        for index, item in enumerate(path_list):
            if 'oracle\\instantclient' in item.lower():
                path_list[index] = oracle_path
                oraclecount = oraclecount + 1
        if oraclecount == 0:
            path_list.append(oracle_path)
        newpath = ';'.join(path_list)   
        os.environ['PATH']=newpath 
        # =============================================================================
        
        # password = keyring.get_password('ODS_DB', 'nlcrepo') # keyring to retrieve password
        # connection_string = 'nlcrepo/'+password+'@sunprod01.cpoj.cz:1521/nlc' # vyvoje oracle DB pro RPA
        
        password = keyring.get_password('ODS_DB_READ', 'rpa_lpu_read') # keyring to retrieve password
        connection_string = 'rpa_lpu_read/'+password+'@sunprod01.cpoj.cz:1521/ods' # vyvoje oracle DB pro RPA

        if not password:
            raise Exception('Není nastaven keyring: ' + str(__class__))
            
        self.connection_string = connection_string
        self.conn = None
        
    @property
    def conn(self):
        if not self._conn:
            self.conn = oracledb.connect(self.connection_string)
        return self._conn

    @conn.setter
    def conn(self, value):
        self._conn = value
    
    
    def get_Last_CaseOwners (self, caseNo):
        """ Funkce pro načtení posledních dvou vlastníků případu Vrátí tuple (user,user)"""
        caseNo = int(str(caseNo))
        curs = self.conn.cursor()
        sql_query = """ SELECT handler FROM tia_cla_case WHERE cla_case_no = :caseNo"""
        curs.execute(sql_query, {'caseNo':caseNo})            
        rows = curs.fetchall()
        try:
            current_owner = rows[0][0]
        except:
            current_owner = ''
        sql_query = """ select regexp_substr(max(description) keep (dense_rank last order by log_no),'^\w*') from tia_cla_event_log where log_type = 'PLI' and cla_case_no = :caseNo"""
        curs.execute(sql_query, {'caseNo':caseNo})            
        rows = curs.fetchall()
        try:
            last_owner = rows[0][0]
        except:
            last_owner = ''
        curs.close()
        return  current_owner, last_owner
    def get_Last_SubCaseOwners (self, subCaseNo):
        """ Funkce pro načtení posledních dvou vlastníků podpřípadu Vrátí tuple (user,user)"""
        subCaseNo = int(str(subCaseNo))
        curs = self.conn.cursor()
        sql_query = """ SELECT handler FROM tia_cla_subcase WHERE cla_subcase_no = :subCaseNo"""
        curs.execute(sql_query, {'subCaseNo':subCaseNo})            
        rows = curs.fetchall()
        try:
            current_owner = rows[0][0]
        except:
            current_owner = ''
        sql_query = """ select regexp_substr(max(description) keep (dense_rank last order by log_no),'^\w*') from tia_cla_event_log where log_type = 'PPL' and cla_subcase_no = :subCaseNo"""
        curs.execute(sql_query, {'subCaseNo':subCaseNo})            
        rows = curs.fetchall()
        try:
            last_owner = rows[0][0]
        except:
            last_owner = ''
        curs.close()
        return  current_owner, last_owner
    def get_SubCaseStatus (self, subCaseNo): 
        """ Načtení současného stavu podpřípadu DC = zamítnuto, EC = omyl, RO =opětovně otevřeno, OP = otevřeno, CL = uzavřeno"""
        subCaseNo = int(str(subCaseNo))
        curs = self.conn.cursor()
        sql_query = """ SELECT status FROM tia_cla_subcase WHERE cla_subcase_no = :subCaseNo"""
        curs.execute(sql_query, {'subCaseNo':subCaseNo})            
        rows = curs.fetchall()
        try:
            subCaseStatus = rows[0][0]
        except:
            subCaseStatus = ''
        return subCaseStatus 
    def get_ClaimHandlerLimit (self, claimHandlerId): 
        """ Načtení samorevizního limitu likvidátora"""
        claimHandlerId = str(claimHandlerId)
        curs = self.conn.cursor()
        sql_query = """ select limit from tia_tcp_user_qualif_fa where qualification = 'P_PMV' and func_auth = 55 and user_id = :claimHandlerId"""
        curs.execute(sql_query, {'claimHandlerId':claimHandlerId})            
        rows = curs.fetchall()
        try:
            limit = rows[0][0]
        except:
            limit = ''
        return limit 
    
    def get_ClaimHandlerNameAndPhone (self, claimHandlerId): 
        """Zjištění jména (FORENAME), příjmení (SURNAME) a telefonu (PHONE) 
        zadaného uživatele do dictu"""
        claimHandlerId = str(claimHandlerId)
        curs = self.conn.cursor()
        sql_query = """ 
                    select 
                      n.forename FORENAME,
                      n.surname SURNAME,
                      (select max(substr(replace(phone_no,' ',''),-9,9)) keep (dense_rank first order by telephone_type) 
                       from tia_name_telephone where name_id_no = u.user_id_no and telephone_type in ('02','06') and companyid__ = 1) PHONE
                    from tia_top_user u, tia_name n
                    where n.id_no = u.user_id_no
                      and u.user_id = :claimHandlerId
                    """
        curs.execute(sql_query, {'claimHandlerId':claimHandlerId})            
        columns = [col[0] for col in curs.description]
        curs.rowfactory = lambda *args: dict(zip(columns, args))
        rows = curs.fetchall()
        return rows[0] if rows else {}
    
    def celni_skla_podpripady_check (self):
        """Vyhledá nezpracované podpřípady v deníku TTE200 a TTE450 """
         # and s.handler in ('TTE200','TTE450')
        curs = self.conn.cursor()
        sql_query = """with tab as ( 
                            select  
                              s.cla_case_no,
                              cla_subcase_no,
                              s.handler,
                              case
                                --
                                when 1 = (select count(1) from tia_cla_subcase where cla_case_no = s.cla_case_no and status <> 'EC') 
                                  and not exists (select 1 from tia_cla_item where cla_subcase_no = s.cla_subcase_no and newest = 'Y' and status in ('OP','RO')) -- neexistuje otevřený platební předmět
                                  and exists (select 1 from tia_cla_item where cla_subcase_no = s.cla_subcase_no and newest = 'Y') -- existuje alespoň 1 platební předmět
                                  and exists (select /*+ INDEX(tia_acc_item TIA_ACC_ITEM_CLAIM_CASE_NO) */1 from tia_acc_item where claim_case_no = s.cla_case_no and nvl(workpad,'N') = 'N' and source = 2) -- neexistuje zablokovaná platba
                                then 'ClaimToClose#1#' -- PU k uzavření
                                --
                                --when 1 = (select count(1) from tia_cla_subcase where cla_case_no = s.cla_case_no and status <> 'EC') 
                                --  and exists (select 1 from tia_cla_item where cla_subcase_no = s.cla_subcase_no and newest = 'Y' and status in ('OP','RO'))
                                --  and (select max(timestamp) from tia_cla_event_log where cla_subcase_no = s.cla_subcase_no and log_type = 'PPL' and description like '% --> ' || s.handler) < sysdate - 12/24 -- 12 hod. od přidělení
                                -- then 'ClaimToReassign#3#Robot tuto PU nezpracoval ve stanovené lhůtě, převezměte ji a dolikvidujte.'
                                --
                                when 1 < (select count(1) from tia_cla_subcase where cla_case_no = s.cla_case_no and status <> 'EC') 
                                then 'ClaimToReassign#3#Robot nemůže tuto ŠU zpracovat, protože má více než jeden podpřípad. Zkontrolujte prosím spis a škodu dolikvidujte.'
                                --
                                when 1 = (select count(1) from tia_cla_subcase where cla_case_no = s.cla_case_no and status <> 'EC')
                                  and exists (select 1 from tia_cla_item where cla_subcase_no = s.cla_subcase_no and newest = 'Y' and status in ('RO'))
                                  --and exists (select 1 from tia_case_item where case_sub_type = 'REV' and action_id > 0 and claim_no = s.cla_case_no)
                                  and exists (select 1 from tia_case_item ci where case_sub_type = 'REV' and claim_no = s.cla_case_no
                                      and (action_id > 0 or 
                                      (select max(timestamp) from tia_history_log where table_name = 'CASE_ITEM' and type = 'ACTION' and key1 = to_char(ci.request_id)) 
                                      >= (select estimate_date from tia_cla_item where cla_subcase_no = s.cla_subcase_no and newest = 'Y' and status in ('RO'))))
                                then 'ClaimToReassign#3#Škoda byla vrácena revidentem k opravě. Zkontrolujte prosím spis a škodu dolikvidujte.'
                                --
                            end pbr
                            from tia_cla_subcase s --, cla_case c, cla_event e, agreement_line a
                            where s.cla_class in ('123','257','121','252')
                              and s.handler in ('TTE200','TTE450')
                              and s.status = 'OP'
                              and s.first_open_date < sysdate - 1/24 -- 1/24 je jedna hodina (1/24/60 by byla minuta)
                              --and c.cla_case_no = s.cla_case_no
                              --and e.cla_event_no = c.cla_event_no
                              --and a.agr_line_seq_no = c.policy_line_seq_no
                        )      
                        select cla_case_no as "caseNo",
                                cla_subcase_no as "subCaseNo",
                                handler as "claimOwner",
                                (select nvl(max(u.delegate) keep (dense_rank last order by l.log_no), regexp_substr(max(l.description) keep (dense_rank last order by l.log_no),'^\w*'))
                                  from tia_cla_event_log l, tia_tcp_delegate_user u
                                  where l.log_type = 'PPL' 
                                    and l.description like '%' || tab.handler
                                    and u.user_id (+) = regexp_substr(l.description,'^\w*') 
                                    and l.cla_subcase_no = tab.cla_subcase_no) "handler",
                                nvl((select regexp_substr(max(l.description) keep (dense_rank last order by l.log_no),'^\w*') from tia_cla_event_log l, tia_top_user u where l.log_type = 'PPL' and regexp_substr(l.description,'^\w*') = u.user_id and u.dept_no = 'PMV_12' and l.cla_subcase_no = tab.cla_subcase_no),
                                    (select max(l.userid) keep (dense_rank last order by l.log_no) from tia_cla_event_log l, tia_top_user u where l.userid = u.user_id and u.dept_no = 'PMV_12' and l.cla_subcase_no = tab.cla_subcase_no))  "creator_id",
                                regexp_substr(pbr, '#?([^#]*)', 1, 1, 'i', 1 ) as "process_branch",
                                regexp_substr(pbr, '#?([^#]*)', 1, 2, 'i', 1 ) as "PB_Code",
                                regexp_substr(pbr, '#?([^#]*)', 1, 3, 'i', 1 ) as "reason"
                        from tab
                        where pbr is not null"""
        curs.execute(sql_query) 
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        return rows 
    def celni_skla_polozky_check (self):
        """ Vyhledá nezpracované položky spisu v deníku TTE188 a TTE450"""
        # and ci.user_id in ('TTE188','TTE450')
        curs = self.conn.cursor()
        sql_query = """select 
                          ci.claim_no as "caseNo", 
                          ci.cla_subcase_no as "subCaseNo", 
                          ci.case_type,
                          ci.case_sub_type,
                          ci.user_id,
                          ci.action_id,
                          ci.d01 as "creation_date",
                          ci.request_id as "claim_item_id",
                          cia.dms_document_id as "dacp_id",
                          'ClaimItemToReassign' as "process_branch",
                          2 as "pb_code",
                          'Zpracujte prosím tuto položku, která byla přidělena robotovi ke zpracování.' as "reason"
                    from tia_case_item ci, tia_case_item_attachments cia
                    where ci.action_id > 0
                      and ci.action_date < sysdate + 1 
                      and ci.user_id in ('TTE450', 'TTE200')
                      and ci.request_id = cia.request_id (+)
                      and not (ci.case_type = 'POV' and (case_sub_type = '5') ) -- Bez prvotních položek spisu 
                      and ci.case_type not in ('VYPP', 'VYPC', 'VYP', 'FAKT', 'KOM') -- Bez výpočtu plnění
                      and not (ci.case_type = 'POV' and case_sub_type = '4' and ci.User_Comm like '%Zablokovaná platba - id položky%')--bez autorizací platby
                   
                    """
        curs.execute(sql_query) 
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        return rows 
    def pocet_polozek (self, cla_case_no: int = None, cla_subcase_no: int = None, item_type: str = None):
        """ Vrátí počet položek na zadaném případu, pokud je uveden podpřípad tak pouze na podpřípadu"""
        # and ci.user_id in ('TTE188','TTE450')
        curs = self.conn.cursor()
        sql_query = """select count(1) 
                        from tia_case_item ci, tia_case_item_attachments cia 
                        where ci.request_id = cia.request_id  
                          and ci.case_type = :item_type 
                          and ci.claim_no = :cla_case_no 
                          and nvl2(:cla_subcase_no, nvl(ci.cla_subcase_no, :cla_subcase_no), 1) = nvl(:cla_subcase_no, 1)
                    """
        curs.execute(sql_query, {'item_type': item_type,
                                 'cla_case_no' : cla_case_no,
                                 'cla_subcase_no' : cla_subcase_no})
        
        
        rows = curs.fetchall()[0][0]
        return rows 

    def pocet_podpripadu(self, case: int = None, subcase: int = None):
        """ Vrátí počet položek na zadaném případu, pokud je uveden podpřípad tak pouze na podpřípadu"""
        # and ci.user_id in ('TTE188','TTE450')
        curs = self.conn.cursor()
        
        
        # subcase = None
        if case and subcase:
            case = int(case)
            subcase = int(subcase)
            sql_query = """select handler,CLA_SUBCASE_NO
                            from tia_cla_subcase 
                            where CLA_CASE_NO = :case
                            and status in ('OP','RO')
                        """

            curs.execute(sql_query, {'case': int(case),
                                     'subcase': int(subcase)})
            rows = curs.fetchall()
            if rows:
                rows = rows[0][0]
            else:
                rows = None
            return rows 
        else:
            return None
        
        # if case and not subcase:
        #     case = int(case)
        #     sql_query = """select handler
        #                     from tia_cla_subcase 
        #                     where CLA_CASE_NO = :case
        #                     and status in ('OP','RO')"""
        #     curs.execute(sql_query, {'case': int(case)})
        #     rows = curs.fetchall()

        #     if rows:
        #         rows = rows[0][0]
        #     else:
        #         rows = None
        #     return rows             



    def najdi_polozky (self, cla_case_no: int = None, cla_subcase_no: int = None, item_type: str = None):
        """ Vrátí dacpID pro položky na zadaném případu, pokud je uveden podpřípad tak pouze na podpřípadu"""
        # and ci.user_id in ('TTE188','TTE450')
        curs = self.conn.cursor()
        sql_query = """select 
                       ci.claim_no as "cla_case_no",
                       ci.cla_subcase_no as "cla_subcase_no",
                       cia.dms_document_id as "dacp_ID"
                       from tia_case_item ci, tia_case_item_attachments cia 
                       where ci.request_id = cia.request_id  
                       and ci.case_type = :item_type 
                       and ci.claim_no = :cla_case_no 
                       and nvl2(:cla_subcase_no, nvl(ci.cla_subcase_no, :cla_subcase_no), 1) = nvl(:cla_subcase_no, 1)
                    """
        curs.execute(sql_query, {'item_type': item_type,
                                 'cla_case_no' : cla_case_no,
                                 'cla_subcase_no' : cla_subcase_no})
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        return rows 

    def get_valid_email(self, cla_case_no: int = None, client_id: int = None, electronicCom: bool = False):
        """ Vrátí email na klienta pokud je vyplněn v kontaktu na PU nebo pokud je v hlavním kontaktu a je zadán souhlas s el. komunikací"""   
        email = None
        curs = self.conn.cursor()
        
        sql_query = """select contact
                       from tia_tcp_claim_contact
                       where cla_case_no = :cla_case_no and name_id_no = :name_id_no and contact_type = '04'
                    """
        curs.execute(sql_query, {'name_id_no': client_id,
                                 'cla_case_no':cla_case_no})
        rows = curs.fetchall()
        if rows:
            email = rows[0][0]
        else:
            # pokud není kontakt na PU
            if electronicCom == True:
                # je souhlas s El. kom. tak zkusí najít email na hlavním kontaktu
                # sql_query = """select max(phone_no) keep (dense_rank first order by nvl(companyid__,1))
                sql_query = """select max(phone_no) keep (dense_rank last order by timestamp)
                               from tia_name_telephone 
                               where name_id_no = :name_id_no and telephone_type = '04' and phone_no is not null
                            """
                curs.execute(sql_query, {'name_id_no': client_id})
                rows = curs.fetchall()
                if rows:
                    email = rows[0][0]
                    
        curs.close()
        return email 
        
    def najdi_dalsi_podpripady (self, caseno: int = None, subcaseno: int = None):
        """ vrátí počet dalších podpřípadů na PU, kromě toho, co je vložen"""
        # and ci.user_id in ('TTE188','TTE450')
        curs = self.conn.cursor()
        sql_query = """select CLA_SUBCASE_NO, status 
                        from tia_cla_subcase 
                        where CLA_CASE_NO = :caseno and CLA_SUBCASE_NO != :subcaseno and status in ('OP','RO')
                    """
        curs.execute(sql_query, {'caseno' : caseno,
                                 'subcaseno' : subcaseno})
        curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        return len(rows)

    def get_last_update_policy_VIAS(self, caseno: int = None):
        """ Vrátí datum a čas poslední aktualizace VIAS PS, na kterou je registrována PU """
        if caseno == None:
            return
        curs = self.conn.cursor()
        sql_query = """ select a.timestamp
                        from tia_cla_case c, tia_agreement_line a
                        where a.agr_line_seq_no = c.policy_line_seq_no
                          and c.cla_case_no = :caseno
                          and a.product_line_id in ('HAVGZ','POVGZ')
                    """
        curs.execute(sql_query, {'caseno' : caseno})
        rows = curs.fetchall()
        curs.close()
        if rows:
            return rows[0][0]

    def get_pohledavky_k_vyvedeni(self, caseno: int = None):
        """ Vrátí související PU HAV s vyvedenou pohledávkou. 
            Použito v EA_Claims
        """
        if caseno == None:
            return
        curs = self.conn.cursor()
        sql_query = """with asist as (
                        select e.incident_date, c.cla_case_no, c.name_id_no, c.object_id_norm
                        from tia_cla_case c, tia_cla_event e
                        where c.cla_event_no = e.cla_event_no
                          and c.cla_case_no = :caseno
                    )
                    select c.cla_case_no caseno
                    from tia_cla_case c, tia_cla_event e, asist
                    where c.cla_event_no = e.cla_event_no
                      and c.name_id_no = asist.name_id_no
                      and c.object_id_norm = asist.object_id_norm
                      and trunc(e.incident_date) = trunc(asist.incident_date)
                      and c.cla_case_no <> asist.cla_case_no
                      and exists (select 1 from tia_tcp_cl_receivables_out where cla_case = c.cla_case_no and cancelled = '0' and v_trigger = 'INI' and rownum = 1) 
                    --
                    union
                    --
                    select c.cla_case_no caseno
                    from tia_cla_case c, tia_cla_event e, tia_cla_subcase s, tia_tcp_third_party_pov ttpp, asist
                    where c.cla_event_no = e.cla_event_no
                      and trunc(e.incident_date) = trunc(asist.incident_date)
                      and s.cla_case_no = c.cla_case_no
                      and ttpp.third_party_no = s.third_party_no
                      and ttpp.car_reg_no = asist.object_id_norm
                      and c.policy_line_no in (5755833,5756461) -- TFS Škodní zástupce 4163907 a Pověření ČKP-zelená karta 4168062
                    """
        curs.execute(sql_query, {'caseno' : caseno})
        rows = curs.fetchall()
        curs.close()
        if rows:
            return rows[0][0]    

    def get_interni_postih(self, caseno: int = None):
        """ Vrátí související PU POV (RZ na třetí straně) - možný interní postih
            Použito v EA_Claims
        """
        if caseno == None:
            return
        curs = self.conn.cursor()
        sql_query = """with asist as (
                        select e.incident_date, c.object_id_norm
                        from tia_cla_case c, tia_cla_event e
                        where c.cla_event_no = e.cla_event_no
                          and c.cla_case_no = :caseno
                    )
                    select distinct c.cla_case_no caseno
                    from tia_cla_case c, tia_cla_event e, tia_cla_subcase s, tia_tcp_third_party_pov ttpp, asist
                    where c.cla_event_no = e.cla_event_no
                      and s.cla_case_no = c.cla_case_no
                      and trunc(e.incident_date) = trunc(asist.incident_date)
                      and ttpp.third_party_no = s.third_party_no
                      and ttpp.car_reg_no = asist.object_id_norm
                      and s.status <> 'EC'
                    """
        curs.execute(sql_query, {'caseno' : caseno})
        rows = curs.fetchall()
        curs.close()
        if rows:
            return rows[0][0]    

    def N_poskozeny(self):
        """  """
       
        curs = self.conn.cursor()
        sql_query = """select 
                  c.cla_case_no,
                  s.cla_subcase_no
                from tia_cla_subcase s, tia_cla_case c
                where c.cla_case_no = s.cla_case_no
                  and s.first_open_date > trunc(sysdate) - 7
                  and (select 
                         max(decode(q.column_name,'C01',h.C01,'C02',h.C02,'C03',h.C03,'C04',h.C04,'C05',h.C05,'C06',h.C06,'C07',h.C07,'C08',h.C08,'C09',h.C09,'C10',h.C10,
                                             'C11',h.C11,'C12',h.C12,'C13',h.C13,'C14',h.C14,'C15',h.C15,'C16',h.C16,'C17',h.C17,'C18',h.C18,'C19',h.C19,'C20',h.C20,
                                             'C21',h.C21,'C22',h.C22,'C23',h.C23,'C24',h.C24,'C25',h.C25,'C26',h.C26,'C27',h.C27,'C28',h.C28,'C29',h.C29,'C30',h.C30,
                                             'C31',h.C31,'C32',h.C32,'C33',h.C33,'C34',h.C34,'C35',h.C35,'C36',h.C36,'C37',h.C37,'C38',h.C38,'C39',h.C39,'C40',h.C40,
                                             'C41',h.C41,'C42',h.C42,'C43',h.C43,'C44',h.C44,'C45',h.C45,'C46',h.C46,'C47',h.C47,'C48',h.C48)) keep (dense_rank last order by question_date)
                       from tia_cla_question q, tia_cla_question_history h
                       where h.question_class = q.question_class
                         and q.question = '279' -- otázka * * * POŠKOZENÝ
                         and h.cla_subcase_no = s.cla_subcase_no) = 'N'
                  and s.cla_class in ('252','192') -- škoda na vozidle POV
                  and s.status in ('OP','RO')
                  and substr(c.informer_type,1,1) = 'W' -- WEB
                    """
        curs.execute(sql_query)
        rows = curs.fetchall()
        curs.close()
        return rows
 
    def check_riziko(self, code: str = None, policy_no: str = None, date: str = None):
        """ zkontroluje riziko na slouvě podle policy_no, date a code"""
        curs = self.conn.cursor()
        sql_query = f"""select 1
        from tia_agreement_line a, tia_object o, tia_obj_risk_slave ors
        where a.policy_no = {policy_no}
          and a.agr_line_no = o.agr_line_no
          and a.trans_id = o.trans_id
          --and trunc(to_date('{date}','DD.MM.YYYY')) between a.cover_start_date and a.cover_end_date
          and a.cancel_code = 0
          and ors.seq_no = o.seq_no
          and ors.risk_YN = 'Y'
          and ors.risk_no =
                    case 
                        when '{code}' = 'PO' and instr('ADH01,AFH01,AGH01,AHH01,ALH01,ASH01,HAVAR',a.product_line_id) > 0 then 40
                        when '{code}'= 'SO' and instr('AAH01,AHH01,ACH01',a.product_line_id) > 0 then 88
                        when '{code}' = 'AE' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 71
                        when '{code}' = 'AL' and instr('AAH01,AHH01,ACH01',a.product_line_id) > 0 then 86
                        when '{code}' = 'AM' and instr('AAH01,AHH01,ACH01',a.product_line_id) > 0 then 85
                        when '{code}' = 'AP' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 70
                        when '{code}' = 'AR' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 67
                        when '{code}' = 'AS' and instr('AAH01,AHH01,ACH01',a.product_line_id) > 0 then 84
                        when '{code}' = 'DS' and instr('AAH01,AFH01,AFP01,AHH01,ACH01,ALH01,ALP01,ASH01,ASP01,HAVAR',a.product_line_id) > 0 then 57
                        when '{code}' = 'K' and instr('ASHK3',a.product_line_id) > 0 then 38
                        when '{code}' = 'PE' and instr('AHH01,ANH01,ASH01,HAVAR',a.product_line_id) > 0 then 59
                        when '{code}' = 'PK' and instr('ABH01,ABP01,ADH01,ADP01,AEH01,AEP01,AFH01,AFP01,AGH01,AGP01,AHH01,AKH01,AKP01,ALH01,ALP01,ANH01,ASH01,ASP01,ASPK3,ATH01,ATP01,HAVAR',a.product_line_id) > 0 then 38
                        when '{code}' = 'PL' and instr('ANH01',a.product_line_id) > 0 then 60
                        when '{code}' = 'PP' and instr('AFH01',a.product_line_id) > 0 then 52
                        when '{code}' = 'PS' and instr('ABH01,ADH01,AEH01,AEP01,AFH01,AGH01,AHH01,AKH01,AKP01,ALH01,ANH01,ASH01,ASP01,ASPK3,ATH01,ATP01,HAVAR',a.product_line_id) > 0 then 39
                        when '{code}' = 'PT' and instr('ANH01',a.product_line_id) > 0 then 61
                        when '{code}' = 'PV' and instr('ABP01,AEP01,AGP01,AHH01,AKP01,ALP01,ASP01,ASPK3,ATP01,HAVAR',a.product_line_id) > 0 then 49
                        when '{code}' = 'RP' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 68
                        when '{code}' = 'S' and instr('ASHK3',a.product_line_id) > 0 then 39
                        when '{code}' = 'SU' and instr('AHH01,ANH01,ASH01,ASP01,HAVAR',a.product_line_id) > 0 then 58
                        when '{code}' = 'SZ' and instr('AGH01,ALH01,ANH01,ASH01,ASP01',a.product_line_id) > 0 then 53
                        when '{code}' = 'TA' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 69
                        when '{code}' = 'XL' and instr('AAH01,AHH01,ACH01',a.product_line_id) > 0 then 87
                        when '{code}' = 'ZH' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 66
                        when '{code}' = 'ZP' and instr('AHH01,HAVAR',a.product_line_id) > 0 then 65
                        when '{code}' = 'SZ' and instr('AAH01,AGH01,AHH01,ACH01,ALH01,ANH01,ASH01,HAVAR',a.product_line_id) > 0 then 53
                        when '{code}' = 'SZ' and instr('HAVGZ',a.product_line_id) > 0 then 18

                        when a.product_line_id = 'HAVAR' then
                            case 
                                when '{code}' = 'SO' then 80
                                when '{code}' = 'AL' then 78
                                when '{code}' = 'AM' then 77
                                when '{code}' = 'AS' then 76
                                when '{code}' = 'K' then 6
                                when '{code}' = 'S' then 7
                                when '{code}' = 'XL' then 79
                            end
                    end
          and rownum = 1  
                    """
        # print(sql_query)
        curs.execute(sql_query)
        # curs.rowfactory = makeDictFactory(curs)
        rows = curs.fetchall()
        if rows:
            return rows[0][0]
      
    def ODS_PU_existence(self, item, MojeRpa_DB ):
        """ Podle PU hledá PU v ODS, pokud ano, tak se koukne, jestli má se podívá, jestli je vysegmentováno, když je, tak zadá do error code OK, jinak, pokud existuje PU a není segmentováno, tak dá Nepřiřazeno. Jinak píše nenalezeno."""
        
        xx = None
        res = None
        if item.get('STATUS') == 'SUCCESS':
            
            res = item.get('RESULT')
            if res:
                curs = self.conn.cursor()
                sql_query = """select  CLA_CASE_NO, WORKGROUP  from TIA_CLA_CASE
                                where CLA_CASE_NO = :res"""
                                     
                curs.execute(sql_query,{'res': res})
                curs.rowfactory = makeDictFactory(curs)
                rows = curs.fetchall()
                if not rows:
                    MojeRpa_DB.update_robot_queue_status('ROBOT_QUEUE',
                                                          item.get('ID'),
                                                          error_codes = 'Nenalezeno')
                    xx = 'Nenalezeno'
                else:
                    if rows[0].get('WORKGROUP'):
                        MojeRpa_DB.update_robot_queue_status('ROBOT_QUEUE',
                                                              item.get('ID'),
                                                              error_codes = 'OK')  
                        xx = 'OK'
                    else:
                        MojeRpa_DB.update_robot_queue_status('ROBOT_QUEUE',
                                                              item.get('ID'),
                                                              status = 'POSTPONED',
                                                              error_codes = 'Nepřiřazeno',
                                                              BUSSINES_EXCEPT = 'Nepřiřazeno')
                        xx = 'Nepřiřazeno'
                        
                
        else: 
            if item.get('STATUS') == 'FAILED':
                MojeRpa_DB.update_robot_queue_status('ROBOT_QUEUE',
                                                      item.get('ID'),
                                                    error_codes = 'OK') 
                xx = 'OK'
        print (f'{str(res)} {str(xx)}')     
        
        
        
    def get_team_leader_email(self,likv_abb):
        """ vlož zkratku lividátora, dostaneš mail jeho teamleadera"""
       
        curs = self.conn.cursor()
        # sql_query = f"""select 
        #               max(n.phone_no) keep (dense_rank last order by nvl(u.end_date,sysdate)) login
        #             from tia_top_user um, tia_tia_user_profile p, tia_name_telephone n, tia_top_user u
        #             where um.user_id = p.user_id
        #              and p.prof_id = 'CLAIHS'
        #               --and nvl(p.end_date,to_date('31.12.9999')) = to_date('31.12.9999')
        #               and n.name_id_no = um.user_id_no
        #               and n.telephone_type = '04'
        #               and um.dept_no = u.dept_no
        #               and u.user_id = '{likv_abb}' """
                      
        sql_query = f"""select 
                      max(n.phone_no) keep (dense_rank last order by nvl(u.end_date,sysdate)) login
                    from tia_top_user um, tia_tia_user_profile p, tia_name_telephone n, tia_top_user u
                    where um.user_id = p.user_id
                     and p.prof_id = 'CLAIHS'
                      --and nvl(p.end_date,to_date('9999-12-31')) = to_date('9999-12-31')
                      and n.name_id_no = um.user_id_no
                      and n.telephone_type = '04'
                      and um.dept_no = u.dept_no
                      and u.user_id = '{likv_abb}' """              
                      
        curs.execute(sql_query)
        
        rows = curs.fetchall()
        curs.close()
        
        if rows:
            return rows[0][0]
        else:
            return None        
    def get_default_payment_item(self, subcase = None):
        curs = self.conn.cursor()
        sql_query = """ with tab_def as (
                    select null class, null def_subitem 
                    from dual where 1=0 union all
                    select '410', '140' from dual union all  
                    select '411', '150' from dual union all  
                    select '412', '140' from dual union all  
                    select '413', '150' from dual union all  
                    select '440', '140' from dual union all  
                    select '441', '150' from dual union all  
                    select '442', '140' from dual union all  
                    select '443', '150' from dual union all
                    select '445', '372' from dual union all
                    select '477', '1010' from dual union all
                    select '1300', '140' from dual
                )
                select 
                  def_subitem
                from tia_cla_subcase s, tab_def
                where s.cla_class = tab_def.class
                  and s.cla_subcase_no = :subcase
                    """  
        curs.execute(sql_query,{'subcase': str(subcase)})
        rows = curs.fetchall()
        if not rows:
            return None
        else:
            value = rows[0][0]
            return value          
        
class rdn_DB():
     import pyodbc  
     def __init__(self):
         self.connection_string = ("Driver={SQL Server};Server=Libra\Libra;Database=DK_REP;Trusted_Connection=yes;") 

     def check_excluded_paragraphs(self, vznik = None, vin = None, rz = None):
         """ Vrátí důvod s kódem nehody, pokud relace obsahuje vybrané paragrafy, nebo když pachatel není náš pojištěný 
             Použito v procesu EA_CLAIMS
         """
         
         vznik = str(vznik)
         vin = str(vin)
         rz = str(rz)

         cnxn = self.pyodbc.connect(self.connection_string)
         
         sql_query = """select distinct
                          substring( 
                            case 
    						  when exists (select 1 from dbo.dn_ucastnik 
                                            where idzaznampdn = v.idzaznampdn 
                                              and left(oznsegmentu, len(v.oznsegmentu)) != v.oznsegmentu 
                                              and pachatel is not null) then '\nPachatelem je jiný účastník DN'
                              else ''
                            end + 
                            case 
    						  when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizakprovoz like '%§ 5 odst% 2 písm% b)%' 
								  or porusenizakprovoz like '%§5 odst% 2 písm% b)%' 
								  or porusenizakprovoz like '%§ 5 odst% 2 písm% a)%'
								  or porusenizakprovoz like '%§5 odst% 2 písm% a)%')
								  ) then '\nŘízení pod vlivem alkoholu/návykové látky'
    					      when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizakprovoz like '%§ 5 odst% 1 písm% f)%'
						   		  or porusenizakprovoz like '%§5 odst% 1 písm% f)%')
								  ) then '\nOdmítnutí zkoušky na alkohol'		
    					      when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizakprovoz like '%§ 5 odst% 1 písm% g)%'
								  or porusenizakprovoz like '%§5 odst% 1 písm% g)%')
								) then '\nOdmítnutí zkoušky na návykovou látku'	
    						  when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizakprovoz like '%§ 47 odst% 4 písm% c)%'
								  or porusenizakprovoz like '%§47 odst% 4 písm% c)%')
								) then '\nOpuštění místa nehody'
    						  when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizakprovoz like '%§ 3 %' 
								  or porusenizakprovoz like '%§3 %')
								) then '\nŘízení bez ŘO'
                              when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and (porusenizaktrest like '%§ 337 odst% 1 písm% a%'
								  or porusenizaktrest like '%§337 odst% 1 písm% a%')
								) then '\nMaření výkonu úředního rozhodnutí'
    						  when exists (select 1 from dbo.dn_ucastnik where idzaznampdn = z.idzaznampdn
    						    and porusenizaktrest is not null) then '\nPorušení trestního zákona'
                              else ''
       						end + ' (Kód nehody: ' + n.kodutvarupolicie + n.rokdn + n.cislodn  + ')', 2, 255) as duvod
                          from 
                          dbo.dn_nehoda as n 
                          inner join dbo.dn_zaznampdn as z on n.idnehoda = z.idnehoda 
                          inner join dbo.dn_vozidlo as v on v.idzaznampdn = z.idzaznampdn 
                          left join dbo.dn_objstranka as o on v.idzaznampdn = o.idzaznampdn 
						  inner join dbo.dn_protokol as p on p.idzaznampdn = z.idzaznampdn 
                        where z.platnostdo >= GETDATE()
                          and ? between cast(floor(cast(coalesce(o.datumcasdn,p.datumcasoznameni) as float)) as datetime) and cast(floor(cast(coalesce(coalesce(o.datumcasdodn, o.datumcasdn),p.datumcasoznameni) as float)) as datetime)
                          and (v.spzy = coalesce(?,'-1') or v.viny = coalesce(?,'-1'))
                          and (exists (select 1 from dbo.dn_ucastnik 
                                        where idzaznampdn = z.idzaznampdn 
                                          and (porusenizakprovoz like '%§ 5 odst% 2 písm% b)%'
										    or porusenizakprovoz like '%§5 odst% 2 písm% b)%'
                                            or porusenizakprovoz like '%§ 5 odst% 2 písm% a)%'
                                            or porusenizakprovoz like '%§5 odst% 2 písm% a)%'
                                            or porusenizakprovoz like '%§ 5 odst% 1 písm% f)%'
                                            or porusenizakprovoz like '%§5 odst% 1 písm% f)%'
                                            or porusenizakprovoz like '%§ 5 odst% 1 písm% g)%'
                                            or porusenizakprovoz like '%§5 odst% 1 písm% g)%'
                                            or porusenizakprovoz like '%§ 47 odst% 4 písm% c)%'
                                            or porusenizakprovoz like '%§47 odst% 4 písm% c)%'
                                            or porusenizakprovoz like '%§ 3 %'
                                            or porusenizakprovoz like '%§3 %'
                                            or porusenizaktrest is not null
                                              )
                                      )
                              or 
                      		exists (select 1 from dbo.dn_ucastnik 
                                      where idzaznampdn = v.idzaznampdn 
                                        and left(oznsegmentu, len(v.oznsegmentu)) != v.oznsegmentu 
                                        and pachatel is not null)
                              )
                     """                         
         data = pd.read_sql(sql_query, cnxn, params = [vznik, rz, vin])
         
         if len(data) == 0:
             return None
         else:
             return (data.loc[0][0])


class camo_db():
    
    def __init__(self, env = 'DEV'):
        self.env = env
        moje_rpa_db = Rpa_DB(env = 'PROD')
        if self.env == 'PROD':
            user, password = moje_rpa_db.get_cred('CAMOBPM')
            self.connection_string = f'{user}/{password}@sunprod01.cpoj.cz:1521/CAMOBPM'
        else:
            user, password = moje_rpa_db.get_cred('CAMOBPMT')
            self.connection_string = f'{user}/{password}@suntest06.cpoj.cz:1530/CAMOBPMT'
        self.conn = None
        self.status_translate = {'NEW': 1,
                                'IN_PROGRESS': 2,
                                'POSTPONED': 3,
                                'SUCCESS': 4,
                                'FAILED': 5,
                                'SUSPENDED': 6}
        
    @property
    def conn(self):
        if not self._conn:
            self.conn = oracledb.connect(self.connection_string)
        return self._conn

    @conn.setter
    def conn(self, value):
        self._conn = value


    
  # TODO RDN classa s importem ocbc, connection stringem 
  # s metodou check_excluded_paragraphs
  
# rdn = rdn_DB()

# xx = rdn.check_excluded_paragraphs(vznik = '05.06.2022', vin='WF0SXXGCDS9D56643', rz = None)
# MojeODS= Ods_DB()
#  x = MojeODS.najdi_dalsi_podpripady(caseno = 7000565084, subcaseno = 9542944)
# MojeODS.connection_string
# MojeODS.get_Last_CaseOwners(7000565084)
# MojeODS.get_Last_SubCaseOwners(caseNo)
# MojeODS.pocet_polozek(cla_case_no = 7834035 , item_type = 'KRYD')
# MojeODS.get_valid_email(cla_case_no = 6375204, client_id = 32410405, electronicCom = True)
# xxx = MojeODS.celni_skla_podpripady_check()
# MojeODS.najdi_polozky(cla_case_no = 7834035 , item_type = 'KRYD')

# db = Ods_DB(env='PROD')
# db.update_postponed(process_name = 'Aukce_TotalCar', interval_min = 30)

# send_mail_ex('test')
# xx = db.check_riziko()

# mail_dict = {}
# mail_dict['subject'] = 'Můj nový subject'
# mail_dict['text'] = 'nový text'
# mail_dict['recipients'] = '<EMAIL>,<EMAIL>'


# xxx = db.get_db_creds(env = 'DEV', db = 'TIA_TEST_PREP1')
# db.perform_checks('EXIF', 'Techrobot21')

# data = MojeODS.N_poskozeny()
# item_to_process = db.get_new_item('EA_CLAIMS')
# xxx = db.get_new_item_dict('EA_CLAIMS')
# db.celni_skla_loader()
# db.get_cred('TTE452')
# db.robot_skill('TECHROBOT1', 'PMV_AfterCalc')
# db.check_timing('PMV_AfterCalc')
# db.robot_run_permitted('TECHROBOT1', 'PMV_AfterCalc')
# db.set_cred ('', 'ipcp_rpa_lpu_test', 'UDvum81fls0LApsupvPl','')
# creds = db.get_cred('Celni_Skla')

# ss = {}
# ss['password'] = 'Nco25niI'
# ss['client_id'] = '16948bb3-0260-472c-abba-7b194f2ad6e0'
# ss['client_secret'] = '8c87e0af-e0c1-4d9c-98a1-c118f6d6376c'

# passs = json.dumps(ss)
# creds = db.get_cred_by_TIA_login('TTE171')
# print(creds)
# db.update_cred(TIA_LOGIN = 'gclaim', PASSWORD = passs)
# db.get_last_assigned_user_index_EA_CLAIMS(7)
# db.get_last_assigned_user_index(14,'Celni_Skla')
# db.get_new_item(process_name ='EA_CLAIMS', onlyNew = True, repeat_counter = 0, timedelta = '1', itemId = None)
# db.perform_checks(process_name, robot = os.environ['USERNAME'])
