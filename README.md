# Oracle Dashboard s automatickým cache systémem

## Popis
Flask aplikace pro zobrazení dat z Oracle databáze s automatickým cache systémem, který načítá data každé ráno v 6:00.

## Nové funkce

### 🔄 Automatické načítání dat
- Data se automaticky načítají ka<PERSON> den v 6:00 ráno
- Data jsou ukládána do souboru `data_cache.json`
- Web zobrazuje data z cache místo přímého dotazu na databázi

### 📊 Cache systém
- **Cache soubor**: `data_cache.json`
- **Formát**: JSON s metadaty o posledním načtení
- **Thread-safe**: Používá zámky pro bezpečný přístup

### 🔧 Nové endpointy
- `/` - <PERSON><PERSON>n<PERSON> stránka (načítá z cache)
- `/refresh` - Manuální obnovení dat

## Instalace

1. Nainstalujte závislosti:
```bash
pip install -r requirements.txt
```

2. Spusťte aplikaci:
```bash
python app.py
```

## Testování

Pro otestování cache systému spusťte:
```bash
python test_cache.py
```

## Jak to funguje

1. **První spuštění**: Aplikace načte data z databáze a uloží do cache
2. **Scheduler**: Běží na pozadí a každý den v 6:00 obnoví data
3. **Web interface**: Zobrazuje data z cache s informací o posledním načtení
4. **Manuální obnova**: Tlačítko "Obnovit data nyní" pro okamžitou aktualizaci

## Výhody

- ⚡ **Rychlejší načítání** - data se načítají z lokálního souboru
- 🔄 **Automatické aktualizace** - žádná manuální intervence
- 💾 **Offline dostupnost** - data dostupná i při výpadku databáze
- 🛡️ **Thread-safe** - bezpečné pro současný přístup

## Konfigurace

### Změna času automatické obnovy
V souboru `app.py` upravte řádek:
```python
schedule.every().day.at("06:00").do(refresh_data)
```

### Změna umístění cache souboru
V souboru `app.py` upravte:
```python
CACHE_FILE = 'data_cache.json'
```
