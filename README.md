# LZ DASH - Lékařské zprávy Dashboard

## Popis
Produkční Flask aplikace pro zobrazení lékařských zpráv z Oracle databáze s automatickým cache systémem.
Aplikace běží jako Windows Service na portu 8091 a automaticky aktualizuje data každý den v 1:00 ráno.

## Nové funkce

### 🔄 Automatické načítání dat
- Data se automaticky načítají ka<PERSON> den v 6:00 ráno
- Data jsou ukládána do souboru `data_cache.json`
- Web zobrazuje data z cache místo přímého dotazu na databázi

### 📊 Cache systém
- **Cache soubor**: `data_cache.json`
- **Formát**: JSON s metadaty o posledním načtení
- **Thread-safe**: Používá zámky pro bezpečný přístup

### 🔧 Nové endpointy
- `/` - <PERSON><PERSON><PERSON><PERSON> (načítá z cache)
- `/refresh` - Manu<PERSON>ln<PERSON> obnovení dat

## 🚀 Rychlé nasazení na Windows Server 2019

### ⚡ Pokud máte chybu "No module named 'oracledb'":
```cmd
# Rychlá oprava jako Administrator:
quick_fix.bat
```

### Automatická instalace (doporučeno):
1. **Spusťte jako Administrator**: `deploy_server.bat`
2. **Hotovo!** Aplikace běží na http://server:8091

### Manuální instalace:
1. Spusťte jako Administrator: `install_production.bat`
2. Pro správu použijte: `manage_service.bat`

### 🆘 Pokud něco nefunguje:
- **Rychlá oprava**: `quick_fix.bat`
- **Kompletní oprava**: `fix_dependencies.bat`
- **Troubleshooting**: Viz `TROUBLESHOOTING.md`

## 🛠️ Správa aplikace

### Základní příkazy:
- **Spustit**: `start_service.bat`
- **Zastavit**: `stop_service.bat`
- **Správa**: `manage_service.bat`

### Service příkazy:
```cmd
python windows_service.py start    # Spustit
python windows_service.py stop     # Zastavit
python windows_service.py restart  # Restart
python windows_service.py remove   # Odinstalovat
```

## Testování

Pro otestování cache systému spusťte:
```bash
python test_cache.py
```

## Jak to funguje

1. **První spuštění**: Aplikace načte data z databáze a uloží do cache
2. **Scheduler**: Běží na pozadí a každý den v 6:00 obnoví data
3. **Web interface**: Zobrazuje data z cache s informací o posledním načtení
4. **Manuální obnova**: Tlačítko "Obnovit data nyní" pro okamžitou aktualizaci

## Výhody

- ⚡ **Rychlejší načítání** - data se načítají z lokálního souboru
- 🔄 **Automatické aktualizace** - žádná manuální intervence
- 💾 **Offline dostupnost** - data dostupná i při výpadku databáze
- 🛡️ **Thread-safe** - bezpečné pro současný přístup

## Konfigurace

### Změna času automatické obnovy
V souboru `app.py` upravte řádek:
```python
schedule.every().day.at("06:00").do(refresh_data)
```

### Změna umístění cache souboru
V souboru `app.py` upravte:
```python
CACHE_FILE = 'data_cache.json'
```
