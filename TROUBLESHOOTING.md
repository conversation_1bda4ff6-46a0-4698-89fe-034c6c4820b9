# 🔧 LZ DASH - Řešení problémů

## ❌ Chy<PERSON>: "No module named 'oracledb'"

### P<PERSON><PERSON><PERSON><PERSON>:
Chybí Oracle Database driver pro Python

### Řešení:
```cmd
# Rychlá oprava:
fix_dependencies.bat

# NEBO manuálně:
pip install oracledb==1.4.2 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
```

## ❌ Chy<PERSON>: "No module named 'read_DB_data'"

### Příčina:
Soubor read_DB_data.py není v adresáři projektu

### Řešení:
```cmd
# Zkontrolujte, že soubor existuje v aktuálním adresáři:
dir read_DB_data.py

# Pokud neexistuje, zkopírujte ho do adresáře projektu
# NEBO zkontrolujte původní síťovou cestu:
dir \\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources\read_DB_data.py
```

## ❌ Proxy problémy

### Příčina:
CPAS proxy není dostupný nebo špatně nakonfigurovaný

### Řešení:
```cmd
# Test proxy:
python test_proxy.py

# Nastavení proxy:
setup_proxy.bat

# Manuální test:
curl --proxy http://proxyns.cpas.cz:8080 https://pypi.org
```

## ❌ Port 8091 obsazený

### Příčina:
Jiná aplikace už používá port 8091

### Řešení:
```cmd
# Zjistit, co používá port:
netstat -ano | findstr :8091

# Zastavit proces (nahraďte PID):
taskkill /PID [PID] /F

# NEBO změnit port v app_production.py
```

## ❌ Service se nespustí

### Příčina:
Chyba v konfiguraci nebo závislosti

### Řešení:
```cmd
# Zkontrolovat logy:
type logs\service.log

# Reinstalace service:
python windows_service.py remove
python windows_service.py install
python windows_service.py start

# Test bez service:
python app_production.py
```

## 🔍 Diagnostické příkazy

### Kontrola systému:
```cmd
# Python verze
python --version

# Nainstalované balíčky
pip list

# Test všech komponent
python test_db_connection.py

# Test proxy
python test_proxy.py

# Test cache
python test_cache.py
```

### Kontrola aplikace:
```cmd
# Status service
sc query LZDashService

# Kontrola portu
netstat -ano | findstr :8091

# Test HTTP odpovědi
curl http://localhost:8091/status

# Logy aplikace
type logs\lz_dash.log
```

## 🛠️ Krok za krokem řešení

### 1. Základní kontrola:
```cmd
# Spusťte jako Administrator:
fix_dependencies.bat
```

### 2. Test systému:
```cmd
python test_db_connection.py
```

### 3. Pokud stále chyby:
```cmd
# Kompletní reinstalace:
python windows_service.py remove
deploy_server.bat
```

### 4. Manuální test:
```cmd
# Test bez service:
python app_production.py
```

## 📞 Časté chyby a řešení

| Chyba | Řešení |
|-------|--------|
| `No module named 'oracledb'` | `fix_dependencies.bat` |
| `No module named 'read_DB_data'` | Zkontrolovat síťovou cestu |
| `Port 8091 obsazený` | `netstat -ano \| findstr :8091` |
| `Proxy timeout` | `test_proxy.py` |
| `Service se nespustí` | Zkontrolovat `logs\service.log` |

## 🆘 Nouzové řešení

Pokud nic nefunguje:
```cmd
# 1. Úplné vyčištění
python windows_service.py remove

# 2. Manuální instalace závislostí
pip uninstall -y flask pandas schedule waitress pywin32 oracledb
pip install flask pandas schedule waitress pywin32 oracledb --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

# 3. Test
python test_db_connection.py

# 4. Reinstalace
deploy_server.bat
```
