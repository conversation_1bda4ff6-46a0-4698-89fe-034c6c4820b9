@echo off
echo ========================================
echo LZ DASH - Sprava Windows Service
echo ========================================
echo.

REM Kontrola admin práv
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Spousteno s admin pravy
) else (
    echo [CHYBA] Tento skript musi byt spusten jako Administrator!
    pause
    exit /b 1
)

:MENU
echo.
echo Vyberte akci:
echo 1. Spustit service
echo 2. Zastavit service
echo 3. Restart service
echo 4. Status service
echo 5. Zobrazit logy
echo 6. Test pripojeni k databazi
echo 7. Manualni obnova dat
echo 8. Odinstalovat service
echo 9. Ukoncit
echo.

set /p choice="Zadejte cislo (1-9): "

if "%choice%"=="1" goto START_SERVICE
if "%choice%"=="2" goto STOP_SERVICE
if "%choice%"=="3" goto RESTART_SERVICE
if "%choice%"=="4" goto STATUS_SERVICE
if "%choice%"=="5" goto SHOW_LOGS
if "%choice%"=="6" goto TEST_DB
if "%choice%"=="7" goto MANUAL_REFRESH
if "%choice%"=="8" goto UNINSTALL_SERVICE
if "%choice%"=="9" goto EXIT

echo Neplatna volba!
goto MENU

:START_SERVICE
echo.
echo [INFO] Spoustim LZ DASH Service...
python windows_service.py start
if %errorLevel% == 0 (
    echo [OK] Service spusten
    timeout /t 3 /nobreak >nul
    netstat -ano | findstr :8090
) else (
    echo [CHYBA] Chyba pri spousteni service
)
goto MENU

:STOP_SERVICE
echo.
echo [INFO] Zastavuji LZ DASH Service...
python windows_service.py stop
if %errorLevel% == 0 (
    echo [OK] Service zastaven
) else (
    echo [CHYBA] Chyba pri zastavovani service
)
goto MENU

:RESTART_SERVICE
echo.
echo [INFO] Restartuji LZ DASH Service...
python windows_service.py stop
timeout /t 2 /nobreak >nul
python windows_service.py start
if %errorLevel% == 0 (
    echo [OK] Service restartovan
) else (
    echo [CHYBA] Chyba pri restartu service
)
goto MENU

:STATUS_SERVICE
echo.
echo [INFO] Status LZ DASH Service:
sc query LZDashService
echo.
echo [INFO] Port 8090 status:
netstat -ano | findstr :8090
echo.
echo [INFO] Pristup k aplikaci:
curl -s http://localhost:8090/status 2>nul || echo Aplikace neodpovida
goto MENU

:SHOW_LOGS
echo.
echo [INFO] Posledni logy (posledních 20 radku):
if exist "logs\lz_dash.log" (
    powershell "Get-Content logs\lz_dash.log -Tail 20"
) else (
    echo Log soubor neexistuje
)
goto MENU

:TEST_DB
echo.
echo [INFO] Testuji pripojeni k databazi...
python test_db_connection.py
goto MENU

:MANUAL_REFRESH
echo.
echo [INFO] Spoustim manualni obnovu dat...
python manual_refresh.py
goto MENU

:UNINSTALL_SERVICE
echo.
echo [WARNING] Opravdu chcete odinstalovat LZ DASH Service? (y/n)
set /p confirm="Potvrzeni: "
if /i "%confirm%"=="y" (
    echo [INFO] Zastavuji a odinstalovavam service...
    python windows_service.py stop
    python windows_service.py remove
    echo [OK] Service odinstalovan
) else (
    echo [INFO] Odinstalace zrusena
)
goto MENU

:EXIT
echo.
echo Ukoncuji...
exit /b 0
