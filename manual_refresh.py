#!/usr/bin/env python3
"""
Manuální skript pro obnovu dat v cache
Užitečné pro testování nebo jednorázové aktualizace
"""

import sys
from datetime import datetime
import json
import os
import threading
from read_DB_data import Rpa_DB

# Konfigurace
CACHE_FILE = 'data_cache.json'
CACHE_LOCK = threading.Lock()

def Rpa_DB_conn(env):
    """Připojení k databázi"""
    return Rpa_DB(env=env)

def get_oracle_data():
    """Načte data z Oracle databáze"""
    connection = Rpa_DB_conn(env='PROD').conn
    
    try:
        cursor = connection.cursor()
        sql_query = """
                    select 
                    claim_number_web,
                    creation_date,
                    jmeno_web,
                    rodne_cislo_web,
                    event_date_time_web,
                    JSON_VALUE(data1, '$.datum_vystaveni') datum_vystaveni,
                    JSON_VALUE(data1, '$.lekar_jmeno') || ' ' || JSON_VALUE(data1, '$.lekar_prijmeni') lekar_jmeno,
                    JSON_VALUE(data1, '$.lekar_odbornost') lekar_odbornost,
                    JSON_VALUE(data1, '$.lekar_obec') lekar_obec,
                    JSON_VALUE(data1, '$.lekar_ulice') lekar_ulice,
                    JSON_VALUE(data1, '$.lekar_psc') lekar_psc,
                    JSON_VALUE(data1, '$.poj_jmeno') || ' ' || JSON_VALUE(data1, '$.poj_prijmeni') poj_jmeno,
                    JSON_VALUE(data1, '$.rodne_cislo') rodne_cislo,
                    JSON_VALUE(data1, '$.diagnoza_kod') diagnoza_kod,
                    JSON_VALUE(data1, '$.diagnoza_nazev') diagnoza_nazev,
                    JSON_VALUE(data1, '$.diagnoza_slovni_popis') diagnoza_slovni_popis,
                    JSON_VALUE(data1, '$.diagnoza_nazev_lat') diagnoza_nazev_lat,
                    JSON_VALUE(data1, '$.lekarska_zprava') status,
                    JSON_VALUE(data1, '$.duvod_vyrazeni') duvod_vyrazeni,
                    JSON_VALUE(data1, '$.doctypeconfidence') confidence,
                    JSON_VALUE(data1, '$.page_count') page_count
                    from lz_pno lz
                    inner join (select 
                    reference,
                    creation_date,
                    JSON_VALUE(specific_data, '$.claimNumber') claim_number_web,
                    JSON_VALUE(specific_data, '$.insuredname') || ' ' || JSON_VALUE(specific_data, '$.insuredsurname') jmeno_web,
                    JSON_VALUE(specific_data, '$.insuredbirthNumber') rodne_cislo_web,
                    JSON_VALUE(specific_data, '$.eventDateTime') event_date_time_web


                    from robot_queue where process_name = 'OCR_LZ' and specific_data2 = 1) rb on rb.reference = lz.filename
                    where ai_request_type = 1 
                    and creation_date >= SYSDATE - INTERVAL '7' DAY
                    order by creation_date desc
        """
        
        cursor.execute(sql_query)
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        return columns, data
        
    except Exception as e:
        print(f'❌ Chyba při získávání dat: {str(e)}')
        return None, None
    
    finally:
        if 'cursor' in locals():
            cursor.close()
        connection.close()

def save_data_to_cache(columns, data):
    """Uloží data do cache souboru"""
    with CACHE_LOCK:
        try:
            # Převedeme data na serializovatelný formát
            serializable_data = []
            for row in data:
                serializable_row = []
                for cell in row:
                    if hasattr(cell, 'strftime'):  # datetime objekty
                        serializable_row.append(cell.strftime('%Y-%m-%d %H:%M:%S'))
                    else:
                        serializable_row.append(str(cell) if cell is not None else None)
                serializable_data.append(serializable_row)
            
            cache_data = {
                'columns': columns,
                'data': serializable_data,
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cache_timestamp': datetime.now().isoformat()
            }
            
            with open(CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Data uložena do cache: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f'❌ Chyba při ukládání do cache: {str(e)}')

def main():
    """Hlavní funkce pro manuální obnovu dat"""
    print("🔄 Spouštím manuální obnovu dat...")
    print("=" * 50)
    
    start_time = datetime.now()
    print(f"⏰ Začátek: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Načteme data z databáze
    print("📡 Načítám data z Oracle databáze...")
    columns, data = get_oracle_data()
    
    if columns and data:
        print(f"📊 Načteno {len(data)} záznamů s {len(columns)} sloupci")
        
        # Uložíme do cache
        print("💾 Ukládám data do cache...")
        save_data_to_cache(columns, data)
        
        end_time = datetime.now()
        duration = end_time - start_time
        print(f"⏰ Konec: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⚡ Doba trvání: {duration.total_seconds():.2f} sekund")
        print("✅ Obnova dat dokončena úspěšně!")
        
    else:
        print("❌ Chyba při načítání dat z databáze")

if __name__ == '__main__':
    main()
