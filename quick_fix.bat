@echo off
echo ========================================
echo LZ DASH - Rychla oprava "No module named oracledb"
echo ========================================
echo.

echo [INFO] Instaluji chybejici oracledb modul...
echo Proxy: http://proxyns.cpas.cz:8080
echo.

pip install oracledb --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

if %errorLevel% == 0 (
    echo [OK] oracledb uspesne nainstalovany!
    echo.
    echo [INFO] Testuji import...
    python -c "import oracledb; print('✅ oracledb funguje, verze:', oracledb.__version__)"
    
    if %errorLevel% == 0 (
        echo.
        echo [INFO] Testuji databazove pripojeni...
        python test_db_connection.py
        
        if %errorLevel% == 0 (
            echo.
            echo [OK] Problem vyresen! Muzete pokracovat s nasazenim.
            echo Spustte: deploy_server.bat
        ) else (
            echo.
            echo [WARNING] oracledb je nainstalovany, ale stale jsou problemy s databazi
            echo Zkontrolujte TROUBLESHOOTING.md
        )
    )
) else (
    echo [CHYBA] Chyba pri instalaci oracledb!
    echo.
    echo Mozne priciny:
    echo 1. Proxy problem - zkontrolujte: http://proxyns.cpas.cz:8080
    echo 2. Internetove pripojeni
    echo 3. Python verze (potreba 3.8+)
    echo.
    echo Zkuste:
    echo 1. setup_proxy.bat
    echo 2. test_proxy.py
    echo 3. Spustit znovu tento skript
)

echo.
pause
