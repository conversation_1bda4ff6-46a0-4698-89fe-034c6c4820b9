# PowerShell skript pro nasazení LZ DASH na Windows Server 2019
# Spusťte jako Administrator

param(
    [switch]$Force,
    [string]$Port = "8090"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "LZ DASH - PowerShell nasazení" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Kontrola admin práv
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "[CHYBA] Tento skript musí být spuštěn jako Administrator!" -ForegroundColor Red
    Write-Host "Spusťte PowerShell jako Administrator a opakujte" -ForegroundColor Yellow
    Read-Host "Stiskněte Enter pro ukončení"
    exit 1
}

Write-Host "[OK] Spuštěno s admin právy" -ForegroundColor Green

# Kontrola Python
Write-Host ""
Write-Host "[INFO] Kontroluji Python instalaci..." -ForegroundColor Blue
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[OK] Python je dostupný: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[CHYBA] Python není nainstalovaný nebo není v PATH!" -ForegroundColor Red
    Write-Host "Nainstalujte Python 3.8+ z https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "Stiskněte Enter pro ukončení"
    exit 1
}

# Vytvoření adresářů
Write-Host ""
Write-Host "[INFO] Vytvářím potřebné adresáře..." -ForegroundColor Blue
@("logs", "backup") | ForEach-Object {
    if (!(Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
        Write-Host "[OK] Adresář $_ vytvořen" -ForegroundColor Green
    }
}

# Nastavení proxy proměnných
Write-Host ""
Write-Host "[INFO] Nastavuji CPAS proxy..." -ForegroundColor Blue
$env:HTTP_PROXY = "http://proxyns.cpas.cz:8080"
$env:HTTPS_PROXY = "http://proxyns.cpas.cz:8080"
Write-Host "[OK] Proxy proměnné nastaveny" -ForegroundColor Green

# Instalace závislostí
Write-Host ""
Write-Host "[INFO] Instaluji Python závislosti přes proxy..." -ForegroundColor Blue
try {
    $pipUpgrade = pip install --upgrade pip --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org 2>&1
    $pipInstall = pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] Závislosti nainstalovány přes CPAS proxy" -ForegroundColor Green
    } else {
        throw "Pip install failed"
    }
} catch {
    Write-Host "[CHYBA] Chyba při instalaci závislostí!" -ForegroundColor Red
    Write-Host "Zkontrolujte proxy připojení: http://proxyns.cpas.cz:8080" -ForegroundColor Yellow
    Write-Host "Chyba: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Stiskněte Enter pro ukončení"
    exit 1
}

# Test databáze
Write-Host ""
Write-Host "[INFO] Testuji připojení k databázi..." -ForegroundColor Blue
$dbTest = python test_db_connection.py
if ($LASTEXITCODE -eq 0) {
    Write-Host "[OK] Databáze je dostupná" -ForegroundColor Green
} else {
    Write-Host "[WARNING] Problém s databází - zkontrolujte připojení" -ForegroundColor Yellow
}

# Firewall
Write-Host ""
Write-Host "[INFO] Konfiguruji Windows Firewall..." -ForegroundColor Blue
try {
    # Smazání starého pravidla
    Remove-NetFirewallRule -DisplayName "LZ DASH Flask App" -ErrorAction SilentlyContinue
    
    # Přidání nového pravidla
    New-NetFirewallRule -DisplayName "LZ DASH Flask App" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow | Out-Null
    Write-Host "[OK] Firewall pravidlo nastaveno pro port $Port" -ForegroundColor Green
} catch {
    Write-Host "[WARNING] Problém s firewall - možná potřebujete ručně otevřít port $Port" -ForegroundColor Yellow
}

# Kontrola portu
Write-Host ""
Write-Host "[INFO] Kontroluji dostupnost portu $Port..." -ForegroundColor Blue
$portCheck = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
if ($portCheck) {
    Write-Host "[WARNING] Port $Port je již obsazený!" -ForegroundColor Yellow
    if (!$Force) {
        $continue = Read-Host "Pokračovat? Existující service bude zastaven (y/n)"
        if ($continue -ne "y") {
            Write-Host "Nasazení zrušeno" -ForegroundColor Yellow
            exit 1
        }
    }
} else {
    Write-Host "[OK] Port $Port je volný" -ForegroundColor Green
}

# Zastavení a odinstalování existujícího service
Write-Host ""
Write-Host "[INFO] Zastavuji existující service..." -ForegroundColor Blue
python windows_service.py stop 2>$null
python windows_service.py remove 2>$null

# Instalace nového service
Write-Host ""
Write-Host "[INFO] Instaluji Windows Service..." -ForegroundColor Blue
python windows_service.py install
if ($LASTEXITCODE -eq 0) {
    Write-Host "[OK] Service nainstalován" -ForegroundColor Green
} else {
    Write-Host "[CHYBA] Chyba při instalaci service!" -ForegroundColor Red
    Read-Host "Stiskněte Enter pro ukončení"
    exit 1
}

# Spuštění service
Write-Host ""
Write-Host "[INFO] Spouštím Windows Service..." -ForegroundColor Blue
python windows_service.py start
if ($LASTEXITCODE -eq 0) {
    Write-Host "[OK] Service spuštěn" -ForegroundColor Green
} else {
    Write-Host "[CHYBA] Chyba při spouštění service!" -ForegroundColor Red
    Read-Host "Stiskněte Enter pro ukončení"
    exit 1
}

# Čekání na spuštění
Write-Host ""
Write-Host "[INFO] Čekám na spuštění aplikace..." -ForegroundColor Blue
Start-Sleep -Seconds 10

# Kontrola běhu
$portCheck = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
if ($portCheck) {
    Write-Host "[OK] Aplikace běží na portu $Port" -ForegroundColor Green
    
    # Test HTTP odpovědi
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port/status" -TimeoutSec 5 -ErrorAction Stop
        Write-Host "[OK] Aplikace odpovídá na HTTP požadavky" -ForegroundColor Green
    } catch {
        Write-Host "[WARNING] Aplikace možná ještě není připravena" -ForegroundColor Yellow
    }
} else {
    Write-Host "[WARNING] Aplikace možná ještě neběží - zkontrolujte za chvíli" -ForegroundColor Yellow
}

# Shrnutí
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "NASAZENÍ DOKONČENO!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 Aplikace je dostupná na:" -ForegroundColor White
Write-Host "   http://localhost:$Port" -ForegroundColor Yellow
Write-Host "   http://$env:COMPUTERNAME`:$Port" -ForegroundColor Yellow
Write-Host ""
Write-Host "📊 Status aplikace:" -ForegroundColor White
Write-Host "   http://localhost:$Port/status" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔧 Správa service:" -ForegroundColor White
Write-Host "   manage_service.bat" -ForegroundColor Yellow
Write-Host ""
Write-Host "📁 Logy najdete v adresáři: logs\" -ForegroundColor White
Write-Host ""

Read-Host "Stiskněte Enter pro ukončení"
