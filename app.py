from flask import Flask, render_template
import pandas as pd
from datetime import datetime
import sys
import json
import os
import threading
import schedule
import time
sys.path.append(r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources')
from read_DB_data import Rpa_DB

app = Flask(__name__)

# Konfigurace pro cache
CACHE_FILE = 'data_cache.json'
CACHE_LOCK = threading.Lock()


MojeRpa_DB = ""

def Rpa_DB_conn(env):
    global MojeRpa_DB
    if not MojeRpa_DB:
        MojeRpa_DB = Rpa_DB(env = env)
    return MojeRpa_DB

def get_oracle_data():
    """Načte data z Oracle databáze"""
    connection = Rpa_DB_conn(env = 'PROD').conn

    try:
        cursor = connection.cursor()
        sql_query = """
                    select
                    claim_number_web,
                    creation_date,
                    jmeno_web,
                    rodne_cislo_web,
                    event_date_time_web,
                    JSON_VALUE(data1, '$.datum_vystaveni') datum_vystaveni,
                    JSON_VALUE(data1, '$.lekar_jmeno') || ' ' || JSON_VALUE(data1, '$.lekar_prijmeni') lekar_jmeno,
                    JSON_VALUE(data1, '$.lekar_odbornost') lekar_odbornost,
                    JSON_VALUE(data1, '$.lekar_obec') lekar_obec,
                    JSON_VALUE(data1, '$.lekar_ulice') lekar_ulice,
                    JSON_VALUE(data1, '$.lekar_psc') lekar_psc,
                    JSON_VALUE(data1, '$.poj_jmeno') || ' ' || JSON_VALUE(data1, '$.poj_prijmeni') poj_jmeno,
                    JSON_VALUE(data1, '$.rodne_cislo') rodne_cislo,
                    JSON_VALUE(data1, '$.diagnoza_kod') diagnoza_kod,
                    JSON_VALUE(data1, '$.diagnoza_nazev') diagnoza_nazev,
                    JSON_VALUE(data1, '$.diagnoza_slovni_popis') diagnoza_slovni_popis,
                    JSON_VALUE(data1, '$.diagnoza_nazev_lat') diagnoza_nazev_lat,
                    JSON_VALUE(data1, '$.lekarska_zprava') status,
                    JSON_VALUE(data1, '$.duvod_vyrazeni') duvod_vyrazeni,
                    JSON_VALUE(data1, '$.doctypeconfidence') confidence,
                    JSON_VALUE(data1, '$.page_count') page_count
                    from lz_pno lz
                    inner join (select
                    reference,
                    creation_date,
                    JSON_VALUE(specific_data, '$.claimNumber') claim_number_web,
                    JSON_VALUE(specific_data, '$.insuredname') || ' ' || JSON_VALUE(specific_data, '$.insuredsurname') jmeno_web,
                    JSON_VALUE(specific_data, '$.insuredbirthNumber') rodne_cislo_web,
                    JSON_VALUE(specific_data, '$.eventDateTime') event_date_time_web


                    from robot_queue where process_name = 'OCR_LZ' and specific_data2 = 1) rb on rb.reference = lz.filename
                    where ai_request_type = 1
                    and creation_date >= SYSDATE - INTERVAL '7' DAY
                    order by creation_date desc
        """

        cursor.execute(sql_query)
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()

        return columns, data

    except Exception as e:
        print(f'Chyba při získávání dat: {str(e)}')
        return None, None

    finally:
        if 'cursor' in locals():
            cursor.close()
        connection.close()

def save_data_to_cache(columns, data):
    """Uloží data do cache souboru"""
    with CACHE_LOCK:
        try:
            # Převedeme data na serializovatelný formát
            serializable_data = []
            for row in data:
                serializable_row = []
                for cell in row:
                    if hasattr(cell, 'strftime'):  # datetime objekty
                        serializable_row.append(cell.strftime('%Y-%m-%d %H:%M:%S'))
                    else:
                        serializable_row.append(str(cell) if cell is not None else None)
                serializable_data.append(serializable_row)

            cache_data = {
                'columns': columns,
                'data': serializable_data,
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cache_timestamp': datetime.now().isoformat()
            }

            with open(CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            print(f"Data uložena do cache: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f'Chyba při ukládání do cache: {str(e)}')

def load_data_from_cache():
    """Načte data z cache souboru"""
    with CACHE_LOCK:
        try:
            if os.path.exists(CACHE_FILE):
                with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                return cache_data['columns'], cache_data['data'], cache_data['last_update']
            else:
                return None, None, None
        except Exception as e:
            print(f'Chyba při načítání z cache: {str(e)}')
            return None, None, None

def refresh_data():
    """Obnoví data z databáze a uloží do cache"""
    print(f"Spouštím obnovu dat: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    columns, data = get_oracle_data()
    if columns and data:
        save_data_to_cache(columns, data)
        print(f"Data úspěšně obnovena: {len(data)} záznamů")
    else:
        print("Chyba při obnově dat z databáze")

@app.route('/')
def index():
    """Hlavní stránka - načte data z cache nebo z databáze"""
    # Nejdříve zkusíme načíst z cache
    columns, data, last_update = load_data_from_cache()

    if columns and data:
        return render_template('report.html',
                             columns=columns,
                             data=data,
                             last_update=last_update)
    else:
        # Pokud cache neexistuje, načteme data přímo z databáze
        print("Cache neexistuje, načítám data z databáze...")
        columns, data = get_oracle_data()
        if columns and data:
            # Uložíme do cache pro příště
            save_data_to_cache(columns, data)
            last_update = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return render_template('report.html',
                                 columns=columns,
                                 data=data,
                                 last_update=last_update)
        return "Chyba při načítání dat"

@app.route('/refresh')
def manual_refresh():
    """Manuální obnovení dat"""
    refresh_data()
    return "Data byla obnovena. <a href='/'>Zpět na hlavní stránku</a>"

def run_scheduler():
    """Spustí scheduler v separátním vlákně"""
    while True:
        schedule.run_pending()
        time.sleep(60)  # Kontrola každou minutu

def setup_scheduler():
    """Nastaví automatické obnovy dat"""
    # Naplánuje obnovu každý den v 6:00 ráno
    schedule.every().day.at("01:00").do(refresh_data)

    # Spustí scheduler v separátním vlákně
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    print("Scheduler nastaven - data se budou obnovovat každý den v 6:00")

if __name__ == '__main__':
    # Nastavíme scheduler
    setup_scheduler()

    # Při prvním spuštění načteme data, pokud cache neexistuje
    if not os.path.exists(CACHE_FILE):
        print("Inicializuji cache při prvním spuštění...")
        refresh_data()

    app.run(host='0.0.0.0', port=5000)
