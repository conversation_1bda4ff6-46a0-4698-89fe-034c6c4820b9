@echo off
echo ========================================
echo LZ DASH - Oprava chybejicich zavislosti
echo ========================================
echo.

echo [INFO] Instaluji chybejici Oracle zavislosti pres CPAS proxy...
echo.

echo [INFO] Instaluji oracledb (Oracle Database driver)...
pip install oracledb==1.4.2 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] oracledb uspesne nainstalovany
) else (
    echo [CHYBA] Chyba pri instalaci oracledb!
    echo.
    echo Zkuste rucne:
    echo pip install oracledb --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
    pause
    exit /b 1
)

echo.
echo [INFO] Kontroluji vsechny zavislosti...
pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] Vsechny zavislosti jsou nainstalovany
) else (
    echo [WARNING] Nektera zavislost mozna chybi
)

echo.
echo [INFO] Testuji nainstalovane moduly...
python -c "import flask; print('Flask:', flask.__version__)"
python -c "import pandas; print('Pandas:', pandas.__version__)"
python -c "import schedule; print('Schedule: OK')"
python -c "import waitress; print('Waitress: OK')"
python -c "import oracledb; print('OracleDB:', oracledb.__version__)"

echo.
echo [INFO] Testuji pripojeni k databazi...
python test_db_connection.py

if %errorLevel% == 0 (
    echo.
    echo [OK] Vsechny zavislosti jsou opraveny!
    echo Nyni muzete spustit: deploy_server.bat
) else (
    echo.
    echo [WARNING] Stale jsou problemy - zkontrolujte chybove hlasky vyse
)

echo.
pause
