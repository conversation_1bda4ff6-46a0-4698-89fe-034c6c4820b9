from flask import Flask, render_template
import pandas as pd
from datetime import datetime
import sys
import json
import os
import threading
import schedule
import time
import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path

# Přidání cesty k RPA modulům
sys.path.append(r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources')
from read_DB_data import Rpa_DB

app = Flask(__name__)

# Konfigurace pro cache
CACHE_FILE = 'data_cache.json'
CACHE_LOCK = threading.Lock()

# Nastavení logování pro produkci
def setup_logging():
    """Nastaví logování pro produkční prostředí"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Rotující log soubory
    file_handler = RotatingFileHandler(
        log_dir / 'lz_dash.log', 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    
    # Nastavení root loggeru
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )
    
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)

# Globální proměnná pro DB připojení
MojeRpa_DB = ""

def Rpa_DB_conn(env):
    global MojeRpa_DB
    if not MojeRpa_DB:
        MojeRpa_DB = Rpa_DB(env=env)
    return MojeRpa_DB

def get_oracle_data():
    """Načte data z Oracle databáze"""
    connection = None
    cursor = None
    
    try:
        app.logger.info("Začínám načítání dat z Oracle databáze...")
        connection = Rpa_DB_conn(env='PROD').conn
        cursor = connection.cursor()
        
        sql_query = """
                    select 
                    claim_number_web,
                    creation_date,
                    jmeno_web,
                    rodne_cislo_web,
                    event_date_time_web,
                    JSON_VALUE(data1, '$.datum_vystaveni') datum_vystaveni,
                    JSON_VALUE(data1, '$.lekar_jmeno') || ' ' || JSON_VALUE(data1, '$.lekar_prijmeni') lekar_jmeno,
                    JSON_VALUE(data1, '$.lekar_odbornost') lekar_odbornost,
                    JSON_VALUE(data1, '$.lekar_obec') lekar_obec,
                    JSON_VALUE(data1, '$.lekar_ulice') lekar_ulice,
                    JSON_VALUE(data1, '$.lekar_psc') lekar_psc,
                    JSON_VALUE(data1, '$.poj_jmeno') || ' ' || JSON_VALUE(data1, '$.poj_prijmeni') poj_jmeno,
                    JSON_VALUE(data1, '$.rodne_cislo') rodne_cislo,
                    JSON_VALUE(data1, '$.diagnoza_kod') diagnoza_kod,
                    JSON_VALUE(data1, '$.diagnoza_nazev') diagnoza_nazev,
                    JSON_VALUE(data1, '$.diagnoza_slovni_popis') diagnoza_slovni_popis,
                    JSON_VALUE(data1, '$.diagnoza_nazev_lat') diagnoza_nazev_lat,
                    JSON_VALUE(data1, '$.lekarska_zprava') status,
                    JSON_VALUE(data1, '$.duvod_vyrazeni') duvod_vyrazeni,
                    JSON_VALUE(data1, '$.doctypeconfidence') confidence,
                    JSON_VALUE(data1, '$.page_count') page_count
                    from lz_pno lz
                    inner join (select 
                    reference,
                    creation_date,
                    JSON_VALUE(specific_data, '$.claimNumber') claim_number_web,
                    JSON_VALUE(specific_data, '$.insuredname') || ' ' || JSON_VALUE(specific_data, '$.insuredsurname') jmeno_web,
                    JSON_VALUE(specific_data, '$.insuredbirthNumber') rodne_cislo_web,
                    JSON_VALUE(specific_data, '$.eventDateTime') event_date_time_web
                    from robot_queue where process_name = 'OCR_LZ' and specific_data2 = 1) rb on rb.reference = lz.filename
                    where ai_request_type = 1 
                    and creation_date >= SYSDATE - INTERVAL '7' DAY
                    order by creation_date desc
        """
        
        cursor.execute(sql_query)
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        app.logger.info(f"Úspěšně načteno {len(data)} záznamů s {len(columns)} sloupci")
        return columns, data
        
    except Exception as e:
        app.logger.error(f'Chyba při získávání dat z databáze: {str(e)}')
        return None, None
    
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def save_data_to_cache(columns, data):
    """Uloží data do cache souboru"""
    with CACHE_LOCK:
        try:
            app.logger.info("Ukládám data do cache...")
            
            # Převedeme data na serializovatelný formát
            serializable_data = []
            for row in data:
                serializable_row = []
                for cell in row:
                    if hasattr(cell, 'strftime'):  # datetime objekty
                        serializable_row.append(cell.strftime('%Y-%m-%d %H:%M:%S'))
                    else:
                        serializable_row.append(str(cell) if cell is not None else None)
                serializable_data.append(serializable_row)
            
            cache_data = {
                'columns': columns,
                'data': serializable_data,
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cache_timestamp': datetime.now().isoformat(),
                'record_count': len(data)
            }
            
            # Backup starého cache
            if os.path.exists(CACHE_FILE):
                backup_file = f"backup/cache_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                os.makedirs("backup", exist_ok=True)
                os.rename(CACHE_FILE, backup_file)
                app.logger.info(f"Starý cache zálohován jako: {backup_file}")
            
            with open(CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            app.logger.info(f"Data úspěšně uložena do cache: {len(data)} záznamů")
            
        except Exception as e:
            app.logger.error(f'Chyba při ukládání do cache: {str(e)}')

def load_data_from_cache():
    """Načte data z cache souboru"""
    with CACHE_LOCK:
        try:
            if os.path.exists(CACHE_FILE):
                with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                app.logger.info(f"Data načtena z cache: {cache_data.get('record_count', 'neznámý počet')} záznamů")
                return cache_data['columns'], cache_data['data'], cache_data['last_update']
            else:
                app.logger.warning("Cache soubor neexistuje")
                return None, None, None
        except Exception as e:
            app.logger.error(f'Chyba při načítání z cache: {str(e)}')
            return None, None, None

def refresh_data():
    """Obnoví data z databáze a uloží do cache"""
    app.logger.info("=== SPOUŠTÍM AUTOMATICKOU OBNOVU DAT ===")
    start_time = datetime.now()
    
    try:
        columns, data = get_oracle_data()
        if columns and data:
            save_data_to_cache(columns, data)
            duration = datetime.now() - start_time
            app.logger.info(f"=== OBNOVA DAT DOKONČENA za {duration.total_seconds():.2f}s ===")
        else:
            app.logger.error("=== OBNOVA DAT SELHALA - žádná data z databáze ===")
    except Exception as e:
        app.logger.error(f"=== OBNOVA DAT SELHALA: {str(e)} ===")

@app.route('/')
def index():
    """Hlavní stránka - načte data z cache nebo z databáze"""
    try:
        # Nejdříve zkusíme načíst z cache
        columns, data, last_update = load_data_from_cache()
        
        if columns and data:
            return render_template('report.html', 
                                 columns=columns, 
                                 data=data, 
                                 last_update=last_update)
        else:
            # Pokud cache neexistuje, načteme data přímo z databáze
            app.logger.warning("Cache neexistuje, načítám data z databáze...")
            columns, data = get_oracle_data()
            if columns and data:
                # Uložíme do cache pro příště
                save_data_to_cache(columns, data)
                last_update = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return render_template('report.html', 
                                     columns=columns, 
                                     data=data, 
                                     last_update=last_update)
            return "Chyba při načítání dat - zkontrolujte logy"
            
    except Exception as e:
        app.logger.error(f"Chyba v hlavní stránce: {str(e)}")
        return f"Chyba aplikace: {str(e)}"

@app.route('/refresh')
def manual_refresh():
    """Manuální obnovení dat"""
    try:
        refresh_data()
        return "Data byla obnovena. <a href='/'>Zpět na hlavní stránku</a>"
    except Exception as e:
        app.logger.error(f"Chyba při manuální obnově: {str(e)}")
        return f"Chyba při obnově dat: {str(e)}"

@app.route('/status')
def status():
    """Status endpoint pro monitoring"""
    try:
        cache_exists = os.path.exists(CACHE_FILE)
        cache_size = os.path.getsize(CACHE_FILE) if cache_exists else 0
        
        status_info = {
            'status': 'running',
            'cache_exists': cache_exists,
            'cache_size_mb': round(cache_size / 1024 / 1024, 2),
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        if cache_exists:
            _, _, last_update = load_data_from_cache()
            status_info['last_cache_update'] = last_update
        
        return status_info
        
    except Exception as e:
        return {'status': 'error', 'message': str(e)}

def run_scheduler():
    """Spustí scheduler v separátním vlákně"""
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # Kontrola každou minutu
        except Exception as e:
            app.logger.error(f"Chyba ve scheduleru: {str(e)}")
            time.sleep(300)  # Při chybě čekat 5 minut

def setup_scheduler():
    """Nastaví automatické obnovy dat"""
    # Naplánuje obnovu každý den v 1:00 ráno
    schedule.every().day.at("01:00").do(refresh_data)
    
    # Spustí scheduler v separátním vlákně
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    app.logger.info("Scheduler nastaven - data se budou obnovovat každý den v 1:00")

if __name__ == '__main__':
    # Nastavení logování
    setup_logging()
    
    app.logger.info("=== SPOUŠTÍM LZ DASH APLIKACI ===")
    app.logger.info(f"Pracovní adresář: {os.getcwd()}")
    
    # Nastavíme scheduler
    setup_scheduler()
    
    # Při prvním spuštění načteme data, pokud cache neexistuje
    if not os.path.exists(CACHE_FILE):
        app.logger.info("Inicializuji cache při prvním spuštění...")
        refresh_data()
    
    app.logger.info("Aplikace připravena na portu 8090")
    app.run(host='0.0.0.0', port=8090, debug=False)
