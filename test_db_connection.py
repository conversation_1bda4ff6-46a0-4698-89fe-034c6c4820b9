#!/usr/bin/env python3
"""
Test databázového připojení pro LZ DASH
"""

import sys
import os
from datetime import datetime

# Přidání cesty k RPA modulům
sys.path.append(r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources')

def test_dependencies():
    """Testuje dostupnost všech potřebných závislostí"""
    required_modules = [
        ('flask', 'Flask'),
        ('pandas', 'pandas'),
        ('schedule', 'schedule'),
        ('waitress', 'waitress'),
        ('oracledb', 'oracledb'),
    ]

    print("📦 Testování Python závislostí...")
    missing_modules = []

    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {display_name} - OK")
        except ImportError:
            print(f"❌ {display_name} - CHYBÍ")
            missing_modules.append(display_name)

    if missing_modules:
        print(f"\n❌ Chybějí<PERSON>í moduly: {', '.join(missing_modules)}")
        print("💡 Spusťte: install_dependencies.bat")
        return False

    print("✅ Všechny závislosti jsou dostupné")
    return True

def test_database_connection():
    """Testuje připojení k Oracle databázi"""
    try:
        print("\n🔌 Testování připojení k Oracle databázi...")

        # Test oracledb modulu
        try:
            import oracledb
            print("✅ oracledb modul dostupný")
        except ImportError:
            print("❌ oracledb modul není nainstalován!")
            print("💡 Spusťte: pip install oracledb --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org")
            return False

        # Import RPA modulu
        from read_DB_data import Rpa_DB

        print("✅ RPA modul úspěšně importován")
        
        # Vytvoření připojení
        print("🔗 Vytvářím připojení k databázi...")
        rpa_db = Rpa_DB(env='PROD')
        connection = rpa_db.conn
        
        print("✅ Připojení k databázi úspěšné")
        
        # Test jednoduchého dotazu
        print("📊 Testuji jednoduchý dotaz...")
        cursor = connection.cursor()
        cursor.execute("SELECT SYSDATE FROM DUAL")
        result = cursor.fetchone()
        
        if result:
            print(f"✅ Test dotaz úspěšný - aktuální čas databáze: {result[0]}")
        
        # Test hlavního dotazu (pouze počet záznamů)
        print("📈 Testuji hlavní dotaz (počet záznamů)...")
        test_query = """
            SELECT COUNT(*) 
            FROM lz_pno lz
            INNER JOIN (
                SELECT reference, creation_date,
                JSON_VALUE(specific_data, '$.claimNumber') claim_number_web,
                JSON_VALUE(specific_data, '$.insuredname') || ' ' || JSON_VALUE(specific_data, '$.insuredsurname') jmeno_web,
                JSON_VALUE(specific_data, '$.insuredbirthNumber') rodne_cislo_web,
                JSON_VALUE(specific_data, '$.eventDateTime') event_date_time_web
                FROM robot_queue 
                WHERE process_name = 'OCR_LZ' AND specific_data2 = 1
            ) rb ON rb.reference = lz.filename
            WHERE ai_request_type = 1 
            AND creation_date >= SYSDATE - INTERVAL '7' DAY
        """
        
        cursor.execute(test_query)
        count_result = cursor.fetchone()
        
        if count_result:
            print(f"✅ Hlavní dotaz úspěšný - počet záznamů za posledních 7 dní: {count_result[0]}")
        
        cursor.close()
        connection.close()
        
        print("🎉 Všechny testy databáze prošly úspěšně!")
        return True
        
    except ImportError as e:
        print(f"❌ Chyba při importu RPA modulu: {str(e)}")
        print("💡 Zkontrolujte, že cesta k RPA modulům je správná")
        return False
        
    except Exception as e:
        print(f"❌ Chyba při testování databáze: {str(e)}")
        return False

def test_network_path():
    """Testuje dostupnost síťové cesty k RPA modulům"""
    try:
        rpa_path = r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources'
        print(f"🌐 Testování síťové cesty: {rpa_path}")
        
        if os.path.exists(rpa_path):
            print("✅ Síťová cesta je dostupná")
            
            # Zkontrolujeme, že read_DB_data.py existuje
            db_module_path = os.path.join(rpa_path, 'read_DB_data.py')
            if os.path.exists(db_module_path):
                print("✅ Modul read_DB_data.py nalezen")
                return True
            else:
                print("❌ Modul read_DB_data.py nenalezen")
                return False
        else:
            print("❌ Síťová cesta není dostupná")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování síťové cesty: {str(e)}")
        return False

if __name__ == '__main__':
    print("🧪 Test systému pro LZ DASH")
    print("=" * 60)
    print(f"📅 Čas testu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Pracovní adresář: {os.getcwd()}")
    print()

    # Test závislostí
    deps_ok = test_dependencies()
    print()

    # Test síťové cesty
    network_ok = test_network_path()
    print()

    # Test databáze pouze pokud jsou závislosti a síťová cesta OK
    if deps_ok and network_ok:
        db_ok = test_database_connection()

        if db_ok:
            print("\n🎉 Všechny testy prošly - aplikace je připravena k nasazení!")
            sys.exit(0)
        else:
            print("\n❌ Test databáze selhal")
            sys.exit(1)
    else:
        if not deps_ok:
            print("\n❌ Test závislostí selhal - spusťte install_dependencies.bat")
        if not network_ok:
            print("\n❌ Test síťové cesty selhal")
        sys.exit(1)
