@echo off
echo ========================================
echo LZ DASH - Test portu 8091
echo ========================================
echo.

echo [INFO] Kontroluji dostupnost portu 8091...

netstat -ano | findstr :8091 >nul
if %errorLevel% == 0 (
    echo [WARNING] Port 8091 je obsazeny!
    echo.
    echo Obsazene porty 8091:
    netstat -ano | findstr :8091
    echo.
    echo Co pouziva port 8091:
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8091') do (
        echo PID: %%a
        tasklist /fi "PID eq %%a" 2>nul | findstr /v "INFO:"
    )
    echo.
    echo Mozna reseni:
    echo 1. Zastavit proces: tasklist /fi "PID eq [PID]"
    echo 2. Pouzit jiny port
    echo 3. Restartovat server
) else (
    echo [OK] Port 8091 je VOLNY!
    echo.
    echo Muzete pokracovat s nasazenim:
    echo   deploy_server.bat
)

echo.
echo [INFO] Kontroluji take sousedni porty...
for %%p in (8090 8091 8092 8093) do (
    netstat -ano | findstr :%%p >nul
    if errorlevel 1 (
        echo [OK] Port %%p je volny
    ) else (
        echo [OBSAZENY] Port %%p
    )
)

echo.
pause
