@echo off
echo ========================================
echo LZ DASH - Kontrola pred nasazenim
echo ========================================
echo.

echo [INFO] Kontroluji lokalni nastaveni...
echo Adresar: %CD%
echo.

echo [INFO] Kontroluji pritomnost klicovych souboru...
if exist "read_DB_data.py" (
    echo [OK] read_DB_data.py - nalezen
) else (
    echo [CHYBA] read_DB_data.py - CHYBI!
    echo Zkopirujte read_DB_data.py do tohoto adresare
    pause
    exit /b 1
)

if exist "app_production.py" (
    echo [OK] app_production.py - nalezen
) else (
    echo [CHYBA] app_production.py - CHYBI!
    pause
    exit /b 1
)

if exist "windows_service.py" (
    echo [OK] windows_service.py - na<PERSON>zen
) else (
    echo [CHYBA] windows_service.py - CHYBI!
    pause
    exit /b 1
)

if exist "requirements.txt" (
    echo [OK] requirements.txt - nalezen
) else (
    echo [CHYBA] requirements.txt - CHYBI!
    pause
    exit /b 1
)

echo.
echo [INFO] Testuji Python moduly...
python test_local_setup.py
if %errorLevel% == 0 (
    echo [OK] Lokalni nastaveni je v poradku
) else (
    echo [CHYBA] Problem s lokalnim nastavenim!
    echo.
    echo Mozna reseni:
    echo 1. fix_dependencies.bat
    echo 2. Zkontrolujte, ze read_DB_data.py je v adresari
    echo 3. Zkontrolujte Python zavislosti
    pause
    exit /b 1
)

echo.
echo [INFO] Testuji databazove pripojeni...
python test_db_connection.py
if %errorLevel% == 0 (
    echo [OK] Databaze je dostupna
) else (
    echo [WARNING] Problem s databazi - aplikace se spusti, ale data nebudou dostupna
)

echo.
echo ========================================
echo KONTROLA DOKONCENA!
echo ========================================
echo.
echo ✅ System je pripraven k nasazeni
echo.
echo Dalsi kroky:
echo 1. deploy_server.bat - Kompletni nasazeni
echo 2. python app_production.py - Test bez service
echo.
pause
