#!/usr/bin/env python3
"""
Windows Service pro LZ DASH Flask aplikaci
Pro Windows Server 2019
"""

import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import logging
from pathlib import Path

# Získání cesty k aplikaci
SERVICE_DIR = Path(__file__).parent.absolute()
sys.path.insert(0, str(SERVICE_DIR))

# Přidání cesty k RPA modulům
sys.path.append(r'\\sfsl02\dokumenty\RPA_LPU\_PACKAGES\Python_robots\PYTHON_MasterBot\resources')

class LZDashService(win32serviceutil.ServiceFramework):
    _svc_name_ = "LZDashService"
    _svc_display_name_ = "LZ DASH - Lékařské zprávy Dashboard"
    _svc_description_ = "Flask aplikace pro zobrazení lékařských zpráv s automatickým cache systémem"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        
        # Nastavení pracovního adresáře
        os.chdir(SERVICE_DIR)
        
        # Nastavení logování
        self.setup_logging()

    def setup_logging(self):
        """Nastaví logování pro service"""
        log_dir = SERVICE_DIR / "logs"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "service.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LZDashService')

    def SvcStop(self):
        """Zastaví service"""
        self.logger.info("Zastavuji LZ DASH Service...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        """Spustí service"""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        self.logger.info("Spouštím LZ DASH Service...")
        self.main()

    def main(self):
        """Hlavní funkce service"""
        try:
            # Import produkční aplikace
            from waitress import serve
            from app_production import app

            self.logger.info("Spouštím Flask aplikaci na portu 8090...")

            # Spustí aplikaci pomocí Waitress s produkčními nastaveními
            serve(
                app,
                host='0.0.0.0',
                port=8090,
                threads=8,
                connection_limit=1000,
                cleanup_interval=30,
                channel_timeout=120,
                max_request_body_size=1073741824,  # 1GB
                asyncore_use_poll=True
            )

        except Exception as e:
            self.logger.error(f"Chyba při spouštění aplikace: {str(e)}")
            servicemanager.LogErrorMsg(f"Chyba při spouštění LZ DASH Service: {str(e)}")

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(LZDashService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(LZDashService)
