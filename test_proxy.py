#!/usr/bin/env python3
"""
Test CPAS proxy připojení
"""

import urllib.request
import urllib.error
import socket
from datetime import datetime

def test_proxy_connection():
    """Testuje připojení přes CPAS proxy"""
    proxy_url = "http://proxyns.cpas.cz:8080"
    test_urls = [
        "https://pypi.org",
        "https://files.pythonhosted.org", 
        "https://www.python.org"
    ]
    
    print("🌐 Testování CPAS proxy připojení...")
    print(f"📡 Proxy server: {proxy_url}")
    print("=" * 50)
    
    # Test proxy serveru
    try:
        proxy_host = "proxyns.cpas.cz"
        proxy_port = 8080
        
        print(f"🔌 Testování dostupnosti proxy serveru {proxy_host}:{proxy_port}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((proxy_host, proxy_port))
        sock.close()
        
        if result == 0:
            print("✅ Proxy server je dostupný")
        else:
            print("❌ Proxy server není dostupný")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování proxy serveru: {str(e)}")
        return False
    
    # Test přístupu přes proxy
    proxy_handler = urllib.request.ProxyHandler({
        'http': proxy_url,
        'https': proxy_url
    })
    opener = urllib.request.build_opener(proxy_handler)
    
    success_count = 0
    
    for url in test_urls:
        try:
            print(f"🔍 Testování: {url}")
            
            request = urllib.request.Request(url)
            response = opener.open(request, timeout=10)
            
            if response.getcode() == 200:
                print(f"✅ {url} - OK (HTTP {response.getcode()})")
                success_count += 1
            else:
                print(f"⚠️  {url} - HTTP {response.getcode()}")
                
        except urllib.error.HTTPError as e:
            print(f"❌ {url} - HTTP Error {e.code}")
        except urllib.error.URLError as e:
            print(f"❌ {url} - URL Error: {str(e)}")
        except Exception as e:
            print(f"❌ {url} - Chyba: {str(e)}")
    
    print("=" * 50)
    print(f"📊 Výsledek: {success_count}/{len(test_urls)} testů prošlo")
    
    if success_count == len(test_urls):
        print("🎉 Proxy připojení funguje perfektně!")
        return True
    elif success_count > 0:
        print("⚠️  Proxy připojení funguje částečně")
        return True
    else:
        print("❌ Proxy připojení nefunguje")
        return False

def test_pip_with_proxy():
    """Testuje pip s proxy nastavením"""
    print("\n🐍 Testování pip s proxy...")
    print("=" * 50)
    
    import subprocess
    
    try:
        # Test pip list s proxy
        result = subprocess.run([
            'pip', 'list', '--format=freeze',
            '--proxy=http://proxyns.cpas.cz:8080',
            '--trusted-host=pypi.org',
            '--trusted-host=files.pythonhosted.org'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ pip funguje s proxy nastavením")
            print(f"📦 Nalezeno {len(result.stdout.splitlines())} nainstalovaných balíčků")
            return True
        else:
            print(f"❌ pip chyba: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ pip timeout - proxy možná nereaguje")
        return False
    except Exception as e:
        print(f"❌ Chyba při testování pip: {str(e)}")
        return False

if __name__ == '__main__':
    print("🧪 Test CPAS proxy pro LZ DASH")
    print(f"📅 Čas testu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test proxy připojení
    proxy_ok = test_proxy_connection()
    
    # Test pip s proxy
    pip_ok = test_pip_with_proxy()
    
    print("\n" + "=" * 50)
    if proxy_ok and pip_ok:
        print("🎉 Všechny proxy testy prošly - můžete pokračovat s instalací!")
        exit(0)
    else:
        print("❌ Některé proxy testy selhaly - zkontrolujte nastavení")
        exit(1)
