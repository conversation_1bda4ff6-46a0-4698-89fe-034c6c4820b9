@echo off
echo ========================================
echo LZ DASH - Rych<PERSON> nasazeni na Windows Server 2019
echo ========================================
echo.

REM Kontrola admin práv
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Spousteno s admin pravy
) else (
    echo [CHYBA] Tento skript musi byt spusten jako Administrator!
    echo.
    echo Postup:
    echo 1. Kliknete pravym tlacitkem na tento soubor
    echo 2. Vyberte "Spustit jako administrator"
    echo.
    pause
    exit /b 1
)

echo [INFO] Kontroluji system...
echo OS: %OS%
echo Pocitac: %COMPUTERNAME%
echo Uzivatel: %USERNAME%
echo Datum: %DATE% %TIME%
echo.

echo [INFO] Kontroluji Python...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python je dostupny
    python --version
) else (
    echo [CHYBA] Python neni nainstalovany nebo neni v PATH!
    echo.
    echo Prosim nainstalujte Python 3.8+ z https://www.python.org/downloads/
    echo A nezapomente zatrhnout "Add Python to PATH" pri instalaci
    pause
    exit /b 1
)

echo.
echo [INFO] Pripravuji adresare...
if not exist "logs" (
    mkdir logs
    echo [OK] Adresar logs vytvoren
)
if not exist "backup" (
    mkdir backup
    echo [OK] Adresar backup vytvoren
)

echo.
echo [INFO] Instaluji Python zavislosti pres proxy...
pip install --upgrade pip --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] Zavislosti nainstalovany pres proxy
) else (
    echo [CHYBA] Chyba pri instalaci zavislosti!
    echo Zkontrolujte proxy pripojeni a opakujte
    pause
    exit /b 1
)

echo.
echo [INFO] Testuji pripojeni k databazi...
python test_db_connection.py
if %errorLevel% == 0 (
    echo [OK] Databaze je dostupna
) else (
    echo [WARNING] Problem s databazi - aplikace se spusti, ale data nebudou dostupna
    echo Zkontrolujte sitove pripojeni a pristupove udaje
)

echo.
echo [INFO] Konfiguruji Windows Firewall pro port 8090...
netsh advfirewall firewall delete rule name="LZ DASH Flask App" >nul 2>&1
netsh advfirewall firewall add rule name="LZ DASH Flask App" dir=in action=allow protocol=TCP localport=8090
if %errorLevel% == 0 (
    echo [OK] Firewall pravidlo nastaveno
) else (
    echo [WARNING] Problem s firewall - mozna potrebujete rucne otevrit port 8090
)

echo.
echo [INFO] Kontroluji, zda port 8090 neni obsazeny...
netstat -ano | findstr :8090 >nul
if %errorLevel% == 0 (
    echo [WARNING] Port 8090 je jiz obsazeny!
    echo Aktualne obsazene porty:
    netstat -ano | findstr :8090
    echo.
    set /p continue="Pokracovat? Existujici service bude zastaven (y/n): "
    if /i not "%continue%"=="y" (
        echo Nasazeni zruseno
        pause
        exit /b 1
    )
) else (
    echo [OK] Port 8090 je volny
)

echo.
echo [INFO] Zastavuji existujici service (pokud bezi)...
python windows_service.py stop >nul 2>&1
python windows_service.py remove >nul 2>&1

echo.
echo [INFO] Instaluji novy Windows Service...
python windows_service.py install
if %errorLevel% == 0 (
    echo [OK] Service nainstalovany
) else (
    echo [CHYBA] Chyba pri instalaci service!
    pause
    exit /b 1
)

echo.
echo [INFO] Spoustim Windows Service...
python windows_service.py start
if %errorLevel% == 0 (
    echo [OK] Service spusten
) else (
    echo [CHYBA] Chyba pri spousteni service!
    pause
    exit /b 1
)

echo.
echo [INFO] Cekam na spusteni aplikace...
timeout /t 10 /nobreak >nul

echo.
echo [INFO] Kontroluji, ze aplikace bezi...
netstat -ano | findstr :8090 >nul
if %errorLevel% == 0 (
    echo [OK] Aplikace bezi na portu 8090
    echo.
    echo Testovani pristupu...
    curl -s http://localhost:8090/status >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Aplikace odpovida na HTTP pozadavky
    ) else (
        echo [WARNING] Aplikace mozna jeste neni pripravena
    )
) else (
    echo [WARNING] Aplikace mozna jeste nebezi - zkontrolujte za chvili
)

echo.
echo ========================================
echo NASAZENI DOKONCENO!
echo ========================================
echo.
echo 🌐 Aplikace je dostupna na:
echo    http://localhost:8090
echo    http://%COMPUTERNAME%:8090
echo.
echo 📊 Status aplikace:
echo    http://localhost:8090/status
echo.
echo 🔧 Sprava service:
echo    Spustit:      python windows_service.py start
echo    Zastavit:     python windows_service.py stop
echo    Restart:      python windows_service.py restart
echo    Odinstalovat: python windows_service.py remove
echo.
echo 📁 Logy najdete v: logs\service.log a logs\lz_dash.log
echo.
echo 🛠️  Pro spravu pouzijte: manage_service.bat
echo.
pause
