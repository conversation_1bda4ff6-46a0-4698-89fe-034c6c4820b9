<!DOCTYPE html>
<html>
<head>
    <title>Oracle Report</title>
    <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .container { margin-top: 20px; }
        .last-update { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Lékařské zprávy SK detail</h1>
        
        <div class="last-update">
            <div class="row">
                <div class="col-md-8">
                    <strong>Poslední aktualizace:</strong> {{ last_update }}
                    <br><small class="text-muted">Data se automaticky obnovuj<PERSON> den v 1:00 ráno a jsou 7 dní z<PERSON>.</small>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table id="reportTable" class="table table-striped">
                <thead>
                    <tr>
                        {% for column in columns %}
                        <th>{{ column }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in data %}
                    <tr>
                        {% for cell in row %}
                        <td>{{ cell }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#reportTable').DataTable({
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/cs.json'
                }
            });
        });
    </script>
</body>
</html>
