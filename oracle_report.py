import oracledb
from datetime import datetime
import pandas as pd

# Nastavení připojení k databázi
connection = oracledb.connect(
    user="YOUR_USERNAME",
    password="YOUR_PASSWORD",
    dsn="YOUR_CONNECTION_STRING"  # např. "hostname:port/service_name"
)

def get_data():
    try:
        # Vytvoření cursoru
        cursor = connection.cursor()
        
        # Zde napište váš SQL dotaz
        sql_query = """
        YOUR_SQL_QUERY_HERE
        """
        
        # Vykonání dotazu
        cursor.execute(sql_query)
        
        # Získání dat
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        # Převod dat do pandas DataFrame
        df = pd.DataFrame(data, columns=columns)
        
        # Vytvoření názvu souboru s aktuálním datem
        today = datetime.now().strftime('%Y%m%d')
        filename = f'report_{today}.xlsx'
        
        # Export do Excel souboru
        df.to_excel(filename, index=False)
        print(f'Report byl úspěšně vytvořen a uložen do souboru {filename}')
        
    except Exception as e:
        print(f'Došlo k chybě: {str(e)}')
    
    finally:
        # Uzavření připojení
        if 'cursor' in locals():
            cursor.close()
        connection.close()

if __name__ == '__main__':
    get_data()
