# 🚀 LZ DASH - Nasazení na Windows Server 2019

## 📋 Předpoklady

### Systémové požadavky:
- **OS**: Windows Server 2019
- **Python**: 3.8 nebo novější
- **Práva**: Administrator účet
- **Soubory**: `read_DB_data.py` v adresáři projektu
- **Port**: 8090 (musí být volný)

### P<PERSON>ed instalací zkontrolujte:
```cmd
# Python verze
python --version

# Lokální RPA modul
dir read_DB_data.py

# Volný port
netstat -ano | findstr :8090

# Rychl<PERSON> kontrola
pre_deploy_check.bat
```

## 🎯 Rychlé nasazení (doporučeno)

### Metoda 1: Automatický BAT skript
```cmd
# Spusťte jako Administrator
deploy_server.bat
```

### Metoda 2: PowerShell skript
```powershell
# Spusťte PowerShell jako Administrator
.\Deploy-LZDash.ps1
```

## 🔧 Manuální nasazení

### Krok 1: Příprava prostředí
```cmd
# Vytvoření ad<PERSON>
mkdir logs
mkdir backup

# Nastavení CPAS proxy (doporučeno)
setup_proxy.bat

# Instalace závislostí přes proxy
install_dependencies.bat

# NEBO manuálně s proxy:
pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
```

### Krok 2: Test systému
```cmd
# Test databáze
python test_db_connection.py

# Test cache
python test_cache.py
```

### Krok 3: Instalace Windows Service
```cmd
# Instalace service
python windows_service.py install

# Spuštění service
python windows_service.py start
```

### Krok 4: Konfigurace firewallu
```cmd
# Otevření portu 8090
netsh advfirewall firewall add rule name="LZ DASH Flask App" dir=in action=allow protocol=TCP localport=8090
```

## 📊 Ověření nasazení

### Kontrola běhu aplikace:
```cmd
# Kontrola portu
netstat -ano | findstr :8090

# Status service
sc query LZDashService

# Test HTTP odpovědi
curl http://localhost:8090/status
```

### Přístup k aplikaci:
- **Lokálně**: http://localhost:8090
- **Ze sítě**: http://[server-ip]:8090
- **Status**: http://[server-ip]:8090/status

## 🛠️ Správa aplikace

### Dostupné BAT soubory:
- `deploy_server.bat` - Kompletní nasazení
- `manage_service.bat` - Interaktivní správa
- `start_service.bat` - Rychlé spuštění
- `stop_service.bat` - Rychlé zastavení

### Service operace:
```cmd
# Základní operace
python windows_service.py [start|stop|restart|remove]

# Kontrola statusu
sc query LZDashService
```

## 📁 Struktura souborů

```
LZ_DASH/
├── app.py                    # Původní aplikace (dev)
├── app_production.py         # Produkční aplikace
├── windows_service.py        # Windows Service
├── deploy_server.bat         # Hlavní nasazovací skript
├── manage_service.bat        # Správa service
├── start_service.bat         # Rychlé spuštění
├── stop_service.bat          # Rychlé zastavení
├── Deploy-LZDash.ps1         # PowerShell nasazení
├── test_db_connection.py     # Test databáze
├── requirements.txt          # Python závislosti
├── templates/
│   └── report.html           # Web template
├── logs/                     # Log soubory
│   ├── service.log           # Service logy
│   └── lz_dash.log           # Aplikační logy
├── backup/                   # Zálohy cache
└── data_cache.json           # Cache soubor
```

## 🔍 Monitoring a troubleshooting

### Logy:
- **Service logy**: `logs\service.log`
- **Aplikační logy**: `logs\lz_dash.log`
- **Windows Event Log**: Applications and Services Logs

### Časté problémy:

1. **Port obsazený**:
   ```cmd
   netstat -ano | findstr :8090
   taskkill /PID [PID] /F
   ```

2. **Databáze nedostupná**:
   ```cmd
   python test_db_connection.py
   ```

3. **Service se nespustí**:
   ```cmd
   # Zkontrolujte logy
   type logs\service.log
   
   # Restart service
   python windows_service.py restart
   ```

## ⚙️ Konfigurace

### Změna času aktualizace:
V `app_production.py` upravte:
```python
schedule.every().day.at("01:00").do(refresh_data)
```

### Změna portu:
V `app_production.py` a `windows_service.py` upravte:
```python
port=8090
```

## 🎯 Automatické funkce

- **⏰ Automatická aktualizace**: Každý den v 1:00 ráno
- **💾 Cache systém**: Data uložena v `data_cache.json`
- **🔄 Zálohy**: Automatické zálohy cache v `backup/`
- **📊 Monitoring**: Status endpoint na `/status`
- **🛡️ Logování**: Rotující logy s automatickou archivací
