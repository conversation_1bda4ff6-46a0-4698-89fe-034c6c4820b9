@echo off
echo ========================================
echo LZ DASH - Instalace Python zavislosti
echo ========================================
echo.

echo [INFO] Instaluji Python zavislosti pres CPAS proxy...
echo Proxy: http://proxyns.cpas.cz:8080
echo.

echo [INFO] Aktualizuji pip...
pip install --upgrade pip --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] pip aktualizovan
) else (
    echo [WARNING] Chyba pri aktualizaci pip
)

echo.
echo [INFO] Instaluji zakladni zavislosti...
pip install Flask==2.3.3 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
pip install pandas==2.0.3 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
pip install schedule==1.2.0 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

echo.
echo [INFO] Instaluji Oracle databazove zavislosti...
pip install oracledb==1.4.2 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

echo.
echo [INFO] Instaluji produkčni zavislosti...
pip install waitress==2.1.2 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
pip install pywin32==306 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

echo.
echo [INFO] Instaluji vse z requirements.txt...
pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org

if %errorLevel% == 0 (
    echo.
    echo [OK] Vsechny zavislosti uspesne nainstalovany!
    echo.
    echo Nainstalovane balicky:
    pip list | findstr -i "flask pandas schedule waitress pywin32 oracledb"
) else (
    echo.
    echo [CHYBA] Chyba pri instalaci zavislosti!
    echo.
    echo Mozne priciny:
    echo - Proxy server neni dostupny
    echo - Chyba v proxy nastaveni
    echo - Internetove pripojeni
    echo.
    echo Zkuste:
    echo 1. Zkontrolovat proxy: http://proxyns.cpas.cz:8080
    echo 2. Zkontrolovat internetove pripojeni
    echo 3. Spustit znovu tento skript
)

echo.
pause
