@echo off
echo ========================================
echo LZ DASH - Instalace pro Windows Server 2019
echo ========================================
echo.

REM Kontrola admin práv
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Spousteno s admin pravy
) else (
    echo [CHYBA] Tento skript musi byt spusten jako Administrator!
    echo Kliknete pravym tlacitkem na soubor a vyberte "Spustit jako administrator"
    pause
    exit /b 1
)

echo.
echo [INFO] Kontroluji Python instalaci...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python je nainstalovany
    python --version
) else (
    echo [CHYBA] Python neni nainstalovany nebo neni v PATH!
    echo Nainstalujte Python 3.8+ a pridejte ho do PATH
    pause
    exit /b 1
)

echo.
echo [INFO] Vytvaram adresare...
if not exist "logs" mkdir logs
if not exist "backup" mkdir backup
echo [OK] Adresare vytvoreny

echo.
echo [INFO] Instaluji Python zavislosti pres proxy...
pip install -r requirements.txt --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] Zavislosti nainstalovany pres proxy
) else (
    echo [CHYBA] Chyba pri instalaci zavislosti!
    pause
    exit /b 1
)

echo.
echo [INFO] Instaluji dodatecne zavislosti pro Windows Service pres proxy...
pip install waitress pywin32 --proxy=http://proxyns.cpas.cz:8080 --trusted-host=pypi.org --trusted-host=files.pythonhosted.org
if %errorLevel% == 0 (
    echo [OK] Dodatecne zavislosti nainstalovany pres proxy
) else (
    echo [CHYBA] Chyba pri instalaci dodatecnych zavislosti!
    pause
    exit /b 1
)

echo.
echo [INFO] Konfiguruji Windows Firewall...
netsh advfirewall firewall add rule name="LZ DASH Flask App" dir=in action=allow protocol=TCP localport=8090
if %errorLevel% == 0 (
    echo [OK] Firewall pravidlo pridano pro port 8090
) else (
    echo [WARNING] Chyba pri pridavani firewall pravidla - mozna jiz existuje
)

echo.
echo [INFO] Testuji pripojeni k databazi...
python test_db_connection.py
if %errorLevel% == 0 (
    echo [OK] Pripojeni k databazi funguje
) else (
    echo [WARNING] Chyba pri testovani databaze - zkontrolujte pripojeni
)

echo.
echo [INFO] Instaluji Windows Service...
python windows_service.py install
if %errorLevel% == 0 (
    echo [OK] Windows Service nainstalovany
) else (
    echo [CHYBA] Chyba pri instalaci Windows Service!
    pause
    exit /b 1
)

echo.
echo [INFO] Spoustim Windows Service...
python windows_service.py start
if %errorLevel% == 0 (
    echo [OK] Windows Service spusten
) else (
    echo [CHYBA] Chyba pri spousteni Windows Service!
    pause
    exit /b 1
)

echo.
echo [INFO] Kontroluji, ze aplikace bezi...
timeout /t 5 /nobreak >nul
netstat -ano | findstr :8090 >nul
if %errorLevel% == 0 (
    echo [OK] Aplikace bezi na portu 8090
) else (
    echo [WARNING] Aplikace mozna jeste nebezi - zkontrolujte za chvili
)

echo.
echo ========================================
echo INSTALACE DOKONCENA!
echo ========================================
echo.
echo Aplikace je dostupna na: http://localhost:8090
echo Service name: LZDashService
echo.
echo Uzitecne prikazy:
echo   Zastavit service:  python windows_service.py stop
echo   Spustit service:   python windows_service.py start
echo   Restart service:   python windows_service.py restart
echo   Odinstalovat:      python windows_service.py remove
echo.
echo Logy najdete v adresari: logs\
echo.
pause
