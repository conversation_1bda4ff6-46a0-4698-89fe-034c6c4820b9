@echo off
echo ========================================
echo LZ DASH - Nastaveni CPAS proxy
echo ========================================
echo.

echo [INFO] Nastavuji proxy promenne pro CPAS...

REM Nastavení proxy proměnných pro aktuální session
set HTTP_PROXY=http://proxyns.cpas.cz:8080
set HTTPS_PROXY=http://proxyns.cpas.cz:8080
set http_proxy=http://proxyns.cpas.cz:8080
set https_proxy=http://proxyns.cpas.cz:8080

echo [OK] Proxy promenne nastaveny pro aktualni session:
echo   HTTP_PROXY=%HTTP_PROXY%
echo   HTTPS_PROXY=%HTTPS_PROXY%

echo.
echo [INFO] Vytvaram pip konfiguraci...

REM Vytvoření adres<PERSON>e pro pip config
if not exist "%APPDATA%\pip" mkdir "%APPDATA%\pip"

REM Vytvoření pip.ini s proxy nastavením
echo [global] > "%APPDATA%\pip\pip.ini"
echo proxy = http://proxyns.cpas.cz:8080 >> "%APPDATA%\pip\pip.ini"
echo trusted-host = pypi.org >> "%APPDATA%\pip\pip.ini"
echo                files.pythonhosted.org >> "%APPDATA%\pip\pip.ini"
echo                pypi.python.org >> "%APPDATA%\pip\pip.ini"

echo [OK] Pip konfigurace vytvorena: %APPDATA%\pip\pip.ini

echo.
echo [INFO] Testuji proxy pripojeni...
curl -s --proxy http://proxyns.cpas.cz:8080 https://pypi.org >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Proxy pripojeni funguje
) else (
    echo [WARNING] Problem s proxy pripojenim
    echo Zkontrolujte dostupnost: http://proxyns.cpas.cz:8080
)

echo.
echo [INFO] Testuji pip s proxy...
pip --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] pip je dostupny
    pip list --format=freeze | findstr pip >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] pip funguje s proxy nastavenim
    )
) else (
    echo [WARNING] Problem s pip
)

echo.
echo ========================================
echo PROXY NASTAVENI DOKONCENO!
echo ========================================
echo.
echo Proxy nastaveni:
echo   Server: proxyns.cpas.cz:8080
echo   Konfigurace: %APPDATA%\pip\pip.ini
echo.
echo Nyni muzete spustit:
echo   install_dependencies.bat
echo   deploy_server.bat
echo.
pause
