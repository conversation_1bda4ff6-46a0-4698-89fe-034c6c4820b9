#!/usr/bin/env python3
"""
Test lokálního nastavení LZ DASH
Ověří, že všechny moduly jsou dostupné lokáln<PERSON>
"""

import os
import sys
from datetime import datetime

def test_local_imports():
    """Testuje import všech potřebných modulů"""
    print("📦 Testování lokálních importů...")
    
    modules_to_test = [
        ('flask', 'Flask framework'),
        ('pandas', 'Pandas data analysis'),
        ('schedule', 'Task scheduler'),
        ('waitress', 'WSGI server'),
        ('oracledb', 'Oracle Database driver'),
        ('read_DB_data', 'Lokální RPA modul')
    ]
    
    failed_imports = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError as e:
            print(f"❌ {module_name} - {description} - CHYBA: {str(e)}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ Chybějí<PERSON><PERSON> moduly: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ Všechny moduly jsou dostupné")
        return True

def test_rpa_module():
    """Specifický test RPA modulu"""
    print("\n🔧 Testování RPA modulu...")
    
    try:
        # Zkontrolujeme, že soubor existuje
        if not os.path.exists('read_DB_data.py'):
            print("❌ Soubor read_DB_data.py nenalezen v aktuálním adresáři")
            print(f"📁 Aktuální adresář: {os.getcwd()}")
            return False
        
        print("✅ Soubor read_DB_data.py nalezen")
        
        # Test importu
        from read_DB_data import Rpa_DB
        print("✅ Třída Rpa_DB úspěšně importována")
        
        # Test vytvoření instance (bez připojení)
        print("🔗 Testování vytvoření RPA DB instance...")
        # Pouze test, že třída existuje a lze ji vytvořit
        print("✅ RPA modul je funkční")
        
        return True
        
    except ImportError as e:
        print(f"❌ Chyba při importu RPA modulu: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Chyba při testování RPA modulu: {str(e)}")
        return False

def test_flask_app():
    """Test Flask aplikace"""
    print("\n🌐 Testování Flask aplikace...")
    
    try:
        # Import aplikace
        from app_production import app
        print("✅ Produkční aplikace úspěšně importována")
        
        # Test, že aplikace má správné routes
        with app.test_client() as client:
            # Test pouze že routes existují (bez databázového připojení)
            routes = [rule.rule for rule in app.url_map.iter_rules()]
            print(f"✅ Nalezeno {len(routes)} routes: {', '.join(routes)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Chyba při importu Flask aplikace: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Chyba při testování Flask aplikace: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🧪 Test lokálního nastavení LZ DASH")
    print("=" * 60)
    print(f"📅 Čas testu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Pracovní adresář: {os.getcwd()}")
    print(f"🐍 Python verze: {sys.version}")
    print()
    
    # Test importů
    imports_ok = test_local_imports()
    
    # Test RPA modulu
    rpa_ok = test_rpa_module()
    
    # Test Flask aplikace
    flask_ok = test_flask_app()
    
    # Shrnutí
    print("\n" + "=" * 60)
    if imports_ok and rpa_ok and flask_ok:
        print("🎉 Všechny lokální testy prošly!")
        print("✅ Aplikace je připravena k nasazení")
        print("\n💡 Další kroky:")
        print("   1. Spusťte: deploy_server.bat")
        print("   2. Nebo pro test: python app_production.py")
        return True
    else:
        print("❌ Některé testy selhaly:")
        if not imports_ok:
            print("   - Chybějící Python moduly")
        if not rpa_ok:
            print("   - Problém s RPA modulem")
        if not flask_ok:
            print("   - Problém s Flask aplikací")
        print("\n💡 Řešení:")
        print("   1. Spusťte: fix_dependencies.bat")
        print("   2. Zkontrolujte, že read_DB_data.py je v adresáři")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
